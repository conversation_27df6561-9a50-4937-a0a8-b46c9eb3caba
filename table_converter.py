#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pdfplumber
import re

def extract_pdf_content(pdf_path):
    """提取PDF内容"""
    all_text = ""
    
    with pdfplumber.open(pdf_path) as pdf:
        print(f"PDF总页数: {len(pdf.pages)}")
        
        for page_num, page in enumerate(pdf.pages, 1):
            print(f"正在处理第 {page_num} 页...")
            text = page.extract_text()
            if text:
                all_text += text + "\n"
    
    return all_text

def clean_text(text):
    """清理文本，去掉页眉页脚等"""
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        # 去掉大标题
        if "小笼妈2023年初中实词虚词表" in line:
            continue
            
        # 去掉页眉
        if "睿爸小屋公众号" in line:
            continue
            
        # 去掉页脚
        if re.match(r'^第\s*\d+\s*页\s*$', line):
            continue
            
        # 去掉训练第x天
        if re.match(r'.*训练第\d+天.*', line):
            continue
            
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def convert_to_markdown_table(text):
    """将文本转换为markdown表格格式"""
    lines = text.split('\n')
    
    # 创建markdown表格
    markdown_content = "# 中考文言虚实词\n\n"
    markdown_content += "| 序号 | 例词 | 词义 | 例句 | 出处 |\n"
    markdown_content += "|------|------|------|------|------|\n"
    
    current_row = []
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        # 跳过表头行
        if line.startswith('序号') and '例词' in line:
            continue
            
        # 检查是否是新行开始（以数字开头）
        if re.match(r'^\d+\s+', line):
            # 如果有之前的行，先输出
            if current_row:
                # 补齐缺失的列
                while len(current_row) < 5:
                    current_row.append("")
                markdown_content += "| " + " | ".join(current_row[:5]) + " |\n"
            
            # 开始新行
            current_row = []
            # 分割当前行的内容
            parts = line.split()
            if len(parts) >= 2:
                current_row.append(parts[0])  # 序号
                current_row.append(parts[1])  # 例词
                
                # 剩余部分作为词义、例句、出处
                remaining = ' '.join(parts[2:])
                
                # 尝试分离出处（包含《》和（）的部分）
                source_pattern = r'《([^》]+)》（([^）]+)）'
                source_match = re.search(source_pattern, remaining)
                
                if source_match:
                    # 找到了出处
                    before_source = remaining[:source_match.start()].strip()
                    source_text = source_match.group(0)  # 完整的出处部分
                    after_source = remaining[source_match.end():].strip()
                    
                    # 进一步分离词义和例句
                    # 词义通常以①②③等开头
                    meaning_match = re.match(r'^([①②③④⑤⑥⑦⑧⑨⑩][^①②③④⑤⑥⑦⑧⑨⑩]*)', before_source)
                    if meaning_match:
                        meaning = meaning_match.group(1).strip()
                        example = before_source[len(meaning):].strip()
                        current_row.append(meaning)  # 词义
                        current_row.append(example)  # 例句
                    else:
                        # 没有找到词义标记，整个作为词义
                        current_row.append(before_source)  # 词义
                        current_row.append("")  # 例句
                    
                    current_row.append(source_text)  # 出处
                else:
                    # 没有找到出处，整个作为词义
                    current_row.append(remaining)  # 词义
                    current_row.append("")  # 例句
                    current_row.append("")  # 出处
        else:
            # 继续处理当前行的内容
            if current_row:
                # 检查是否包含出处信息
                source_pattern = r'《([^》]+)》（([^）]+)）'
                source_match = re.search(source_pattern, line)
                
                if source_match:
                    # 这行包含出处信息，可能是例句和出处
                    before_source = line[:source_match.start()].strip()
                    source_text = source_match.group(0)
                    
                    # 如果当前行还没有例句，添加例句
                    if len(current_row) < 4 or not current_row[3]:
                        if len(current_row) < 4:
                            current_row.append(before_source)  # 例句
                        else:
                            current_row[3] = before_source  # 例句
                    
                    # 添加出处
                    if len(current_row) < 5:
                        current_row.append(source_text)  # 出处
                    else:
                        current_row[4] = source_text  # 出处
                else:
                    # 这行可能是词义或例句的延续
                    if len(current_row) >= 3:
                        # 添加到词义或例句中
                        if len(current_row) < 4:
                            current_row.append(line)  # 作为例句
                        else:
                            current_row[3] += " " + line  # 添加到例句
    
    # 输出最后一行
    if current_row:
        while len(current_row) < 5:
            current_row.append("")
        markdown_content += "| " + " | ".join(current_row[:5]) + " |\n"
    
    return markdown_content

def main():
    pdf_path = "中考文言虚实词.pdf"
    
    print("开始提取PDF内容...")
    raw_text = extract_pdf_content(pdf_path)
    
    print("清理文本...")
    cleaned_text = clean_text(raw_text)
    
    # 保存清理后的文本用于调试
    with open("cleaned_table.txt", "w", encoding="utf-8") as f:
        f.write(cleaned_text)
    
    print("转换为markdown表格...")
    markdown_content = convert_to_markdown_table(cleaned_text)
    
    # 保存markdown文件
    with open("中考文言虚实词_表格版.md", "w", encoding="utf-8") as f:
        f.write(markdown_content)
    
    print("转换完成！文件已保存为 中考文言虚实词_表格版.md")
    
    # 显示前几行作为预览
    lines = markdown_content.split('\n')
    print("\n前10行预览：")
    for i, line in enumerate(lines[:10]):
        print(f"{i+1}: {line}")

if __name__ == "__main__":
    main()
