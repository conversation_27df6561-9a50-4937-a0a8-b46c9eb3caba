#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动补充剩余的词性
根据文言文语法知识精确判断
"""

import json

def manual_fill_remaining_pos():
    """手动填充剩余的词性"""
    
    # 手动判断的词性映射
    manual_pos_mapping = {
        # 词条ID: {用法ID: 词性}
        74: {7: "动词"},      # 善刀而藏之 - "善"通"缮"，修理、擦拭，是动词
        77: {6: "动词"},      # 牵涉，涉及，关联 - 动词
        110: {5: "形容词"},   # 寻常，平常，普通 - 形容词
        175: {5: "副词"},     # 苍皇（仓皇），急急忙忙 - 副词
        195: {9: "动词"},     # 辞谢，婉言道歉 - 动词
        203: {6: "副词"},     # 旦旦，天天 - 副词
        204: {8: "名词"},     # 独夫：众叛亲离的君主 - 名词
        232: {5: "名词"},     # 既望：农历每月十六日 - 名词（时间名词）
        232: {6: "副词"},     # 既而：不久 - 副词（时间副词）
        257: {8: "代词"},     # 如……何：对……怎么办 - 疑问代词
        265: {7: "连词"},     # 是故，是以，因此 - 连词
        276: {7: "名词"},     # 既望：农历每月十六日 - 名词（时间名词）
        290: {4: "动词"},     # 再拜：拜两次 - 动词
        291: {6: "副词"},     # 造次：急遽，匆忙 - 副词
    }
    
    # 读取数据
    with open("wenyan_words_pos_filled.json", 'r', encoding='utf-8') as f:
        words_data = json.load(f)
    
    filled_count = 0
    
    # 应用手动判断的词性
    for word_data in words_data:
        word_id = word_data.get('id')
        if word_id in manual_pos_mapping:
            for usage in word_data.get('用法', []):
                usage_id = usage.get('用法id')
                if usage_id in manual_pos_mapping[word_id]:
                    new_pos = manual_pos_mapping[word_id][usage_id]
                    old_pos = usage.get('词性', '')
                    if not old_pos.strip():  # 只填充空的词性
                        usage['词性'] = new_pos
                        filled_count += 1
                        word_name = word_data.get('实词', '')
                        meaning = usage.get('含义', '')[:30]
                        print(f"已填充: 词条{word_id}（{word_name}）用法{usage_id} -> {new_pos}")
                        print(f"  含义: {meaning}...")
    
    # 保存结果
    output_file = "wenyan_words_complete_pos.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(words_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n手动填充完成！")
    print(f"已填充: {filled_count} 个词性")
    print(f"结果保存到: {output_file}")
    
    # 验证是否还有缺失
    missing_count = 0
    for word_data in words_data:
        for usage in word_data.get('用法', []):
            if not usage.get('词性', '').strip():
                missing_count += 1
    
    print(f"剩余缺失: {missing_count} 个词性")
    
    if missing_count == 0:
        print("🎉 所有词性已完整填充！")
    
    return output_file

def verify_pos_completeness(json_file):
    """验证词性完整性"""
    with open(json_file, 'r', encoding='utf-8') as f:
        words_data = json.load(f)
    
    print(f"\n=== 词性完整性验证 ===")
    
    total_usages = 0
    missing_pos = []
    
    for word_data in words_data:
        word_id = word_data.get('id')
        word_name = word_data.get('实词', '')
        
        for usage in word_data.get('用法', []):
            total_usages += 1
            usage_id = usage.get('用法id')
            pos = usage.get('词性', '').strip()
            
            if not pos:
                missing_pos.append(f"词条{word_id}（{word_name}）用法{usage_id}")
    
    print(f"总用法数: {total_usages}")
    print(f"缺失词性: {len(missing_pos)}")
    print(f"完整率: {(total_usages - len(missing_pos)) / total_usages * 100:.1f}%")
    
    if missing_pos:
        print(f"\n缺失词性的用法:")
        for item in missing_pos:
            print(f"  - {item}")
    else:
        print("✅ 所有用法的词性都已完整！")

if __name__ == "__main__":
    # 手动填充剩余词性
    output_file = manual_fill_remaining_pos()
    
    # 验证完整性
    verify_pos_completeness(output_file)
