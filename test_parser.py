#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文言实词解析器
"""

import json
from advanced_wenyan_parser import AdvancedWenyanParser

def test_with_sample_text():
    """使用示例文本测试解析器"""
    
    # 模拟的文言实词文本
    sample_text = """
1、昂
① 动词，仰起，抬起
例：《核舟记》袒胸露乳，矫首昂视
（佛印）袒露胸脯露出乳头，抬起头往上看
② 动词，升高，抬高
例：《促织》昂其直，居为奇货
（市中游侠儿）抬高它的价值，囤积起来当做珍奇的货物

2、安
① 形容词，安定，平静
例：《茅屋为秋风所破歌》风雨不动安如山
风雨吹打也不动摇，安稳得像山一样
② 动词，安置，安排
例：《项羽本纪》项王则受璧，置之坐上，亚父受玉斗，置之地，拔剑撞而破之
项王接受了玉璧，放在座位上，亚父接受了玉斗，放在地上，拔剑撞击把它打破了
"""
    
    parser = AdvancedWenyanParser()
    parser.debug_mode = True
    
    # 分割词条
    entries = parser.split_into_word_entries(sample_text)
    print(f"分割得到 {len(entries)} 个词条")
    
    # 解析每个词条
    for i, entry in enumerate(entries):
        print(f"\n=== 词条 {i+1} ===")
        print(f"原文:\n{entry}")
        
        word_data = parser.parse_word_entry(entry)
        print(f"\n解析结果:")
        print(json.dumps(word_data, ensure_ascii=False, indent=2))
        
        parser.words_data.append(word_data)
    
    # 保存结果
    with open("test_result.json", 'w', encoding='utf-8') as f:
        json.dump(parser.words_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n测试完成！结果已保存到 test_result.json")

def test_pdf_parsing():
    """测试PDF解析"""
    pdf_path = "文言实词300个最新.pdf"
    
    try:
        parser = AdvancedWenyanParser()
        parser.debug_mode = True
        
        # 只解析前3个词条进行测试
        result = parser.parse_pdf_to_json(pdf_path, "test_pdf_result.json", max_words=3)
        
        if result:
            print("PDF解析测试成功！")
        else:
            print("PDF解析测试失败")
            
    except FileNotFoundError:
        print(f"PDF文件不存在: {pdf_path}")
        print("请确保PDF文件在当前目录下")
    except Exception as e:
        print(f"PDF解析出错: {e}")

if __name__ == "__main__":
    print("=== 测试示例文本解析 ===")
    test_with_sample_text()
    
    print("\n" + "="*50)
    print("=== 测试PDF文件解析 ===")
    test_pdf_parsing()
