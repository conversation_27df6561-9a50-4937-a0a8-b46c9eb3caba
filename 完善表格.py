#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完善初中.md表格空白单元格的脚本
功能：
1. 序号和例词和词义：空白单元格，请填充使用上一个有值的序号
2. 读音这一列，空白继续保留
"""

import re

def process_markdown_table(input_file, output_file):
    """
    处理markdown表格，完善空白单元格
    """
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 存储处理后的行
    processed_lines = []
    
    # 记录当前的序号、例词、词义
    current_序号 = ""
    current_例词 = ""
    current_词义 = ""
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 跳过表头和分隔线
        if i <= 1:
            processed_lines.append(line + '\n')
            continue
            
        # 如果不是表格行，直接添加
        if not line.startswith('|') or not line.endswith('|'):
            processed_lines.append(line + '\n')
            continue
            
        # 解析表格行
        cells = [cell.strip() for cell in line.split('|')[1:-1]]  # 去掉首尾空元素
        
        if len(cells) != 6:  # 确保有6列
            processed_lines.append(line + '\n')
            continue
            
        序号, 例词, 读音, 词义, 例句, 出处 = cells
        
        # 处理序号
        if 序号.strip():
            current_序号 = 序号.strip()
        else:
            序号 = current_序号
            
        # 处理例词
        if 例词.strip():
            current_例词 = 例词.strip()
        else:
            例词 = current_例词
            
        # 处理词义
        if 词义.strip():
            current_词义 = 词义.strip()
        else:
            词义 = current_词义
            
        # 读音保持原样（包括空白）
        # 例句和出处保持原样
        
        # 重新构建表格行
        new_line = f"| {序号} | {例词} | {读音} | {词义} | {例句} | {出处} |"
        processed_lines.append(new_line + '\n')
    
    # 写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(processed_lines)
    
    print(f"处理完成！结果已保存到 {output_file}")

def main():
    """
    主函数
    """
    input_file = "初中.md"
    output_file = "初中_完善.md"
    
    try:
        process_markdown_table(input_file, output_file)
        print("表格处理成功！")
        
        # 显示处理统计
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        table_lines = [line for line in lines if line.strip().startswith('|') and line.strip().endswith('|')]
        data_lines = table_lines[2:]  # 去掉表头和分隔线
        
        print(f"共处理了 {len(data_lines)} 行数据")
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {input_file}")
    except Exception as e:
        print(f"处理过程中出现错误：{e}")

if __name__ == "__main__":
    main()
