#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构文言实词数据结构
将每个用法下的例句重构为支持多个例句的格式
"""

import json
import re

class ExampleRestructurer:
    """例句重构器"""
    
    def __init__(self):
        pass
    
    def parse_multiple_examples(self, example_content, example_explanation, example_source):
        """解析可能包含多个例句的内容"""
        examples = []
        
        # 如果例句内容中包含多个书名号，说明可能有多个例句
        book_pattern = r'《([^》]+)》([^《]*?)(?=《|$)'
        book_matches = re.findall(book_pattern, example_content)
        
        if len(book_matches) > 1:
            # 多个例句的情况
            for i, (book, content) in enumerate(book_matches):
                example = {
                    "例句id": i + 1,
                    "出处": f"《{book}》",
                    "内容": content.strip(),
                    "释义": ""
                }
                examples.append(example)
            
            # 尝试分配释义
            if example_explanation:
                explanation_parts = self.split_explanation(example_explanation, len(examples))
                for i, explanation in enumerate(explanation_parts):
                    if i < len(examples):
                        examples[i]["释义"] = explanation
        
        elif example_source or example_content:
            # 单个例句的情况
            example = {
                "例句id": 1,
                "出处": example_source if example_source else "",
                "内容": example_content if example_content else "",
                "释义": example_explanation if example_explanation else ""
            }
            examples.append(example)
        
        return examples
    
    def split_explanation(self, explanation, num_examples):
        """尝试将释义分割为多个部分"""
        if not explanation:
            return [""] * num_examples
        
        # 尝试按照一些标记分割释义
        # 如：《书名》释义内容《书名》释义内容
        book_explanation_pattern = r'《[^》]+》[^《]*'
        matches = re.findall(book_explanation_pattern, explanation)
        
        if len(matches) == num_examples:
            return [match.split('》', 1)[1].strip() if '》' in match else match for match in matches]
        
        # 如果无法精确分割，返回完整释义给第一个例句
        result = [explanation] + [""] * (num_examples - 1)
        return result
    
    def restructure_usage(self, usage):
        """重构单个用法的数据结构"""
        new_usage = {
            "用法id": usage.get("用法id", ""),
            "词性": usage.get("词性", ""),
            "含义": usage.get("含义", ""),
            "例句": []
        }
        
        # 获取原有的例句信息
        example_source = usage.get("例句出处", "")
        example_content = usage.get("例句内容", "")
        example_explanation = usage.get("例句释义", "")
        
        # 解析例句
        examples = self.parse_multiple_examples(example_content, example_explanation, example_source)
        new_usage["例句"] = examples
        
        return new_usage
    
    def restructure_word(self, word_data):
        """重构单个实词的数据结构"""
        new_word = {
            "id": word_data.get("id", ""),
            "实词": word_data.get("实词", ""),
            "用法": []
        }
        
        # 重构每个用法
        for usage in word_data.get("用法", []):
            new_usage = self.restructure_usage(usage)
            new_word["用法"].append(new_usage)
        
        return new_word
    
    def restructure_all_data(self, input_file, output_file=None):
        """重构所有数据"""
        # 读取原始数据
        with open(input_file, 'r', encoding='utf-8') as f:
            words_data = json.load(f)
        
        print(f"开始重构 {len(words_data)} 个实词的数据结构...")
        
        # 重构每个实词
        new_words_data = []
        for i, word_data in enumerate(words_data):
            new_word = self.restructure_word(word_data)
            new_words_data.append(new_word)
            
            if (i + 1) % 50 == 0:
                print(f"已处理 {i + 1} 个实词...")
        
        # 保存结果
        if not output_file:
            output_file = "wenyan_words_restructured.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(new_words_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n重构完成！")
        print(f"结果已保存到: {output_file}")
        
        # 显示统计信息
        self.show_statistics(new_words_data)

        # 显示示例
        self.show_examples(new_words_data)
        
        return output_file
    
def main():
    restructurer = ExampleRestructurer()
    
    # 重构数据结构
    output_file = restructurer.restructure_all_data("wenyan_words_complete_pos.json")
    
    return output_file

if __name__ == "__main__":
    main()
