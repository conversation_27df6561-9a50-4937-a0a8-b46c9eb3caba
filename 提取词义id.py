#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从词义列中提取序号，创建新的词义id列
功能：
1. 从词义列中提取序号（如①、②、③等）
2. 将提取的序号放到新的"词义id"列
3. 从原"词义"列中移除序号
"""

import re

def extract_meaning_id(input_file, output_file):
    """
    从词义列中提取序号，创建新的词义id列
    """
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 存储处理后的行
    processed_lines = []
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 处理表头
        if i == 0:
            # 在词义列后添加词义id列
            processed_lines.append("| 序号 | 例词 | 读音 | 词义 | 词义id | 例句 | 出处 | 年级 |\n")
            continue
        elif i == 1:
            # 添加分隔线
            processed_lines.append("|---|---|---|---|---|---|---|---|\n")
            continue
            
        # 如果不是表格行，直接添加
        if not line.startswith('|') or not line.endswith('|'):
            processed_lines.append(line + '\n')
            continue
            
        # 解析表格行
        cells = [cell.strip() for cell in line.split('|')[1:-1]]  # 去掉首尾空元素
        
        if len(cells) != 7:  # 确保有7列
            processed_lines.append(line + '\n')
            continue
            
        序号, 例词, 读音, 词义, 例句, 出处, 年级 = cells
        
        # 使用正则表达式提取词义序号
        # 匹配模式：①、②、③、④、⑤等圆圈数字
        meaning_id_pattern = r'^([①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳])'
        meaning_id_match = re.match(meaning_id_pattern, 词义)
        
        # 提取词义id
        if meaning_id_match:
            词义id = meaning_id_match.group(1)
            # 从词义中移除序号
            词义_cleaned = re.sub(meaning_id_pattern, '', 词义).strip()
        else:
            词义id = ""
            词义_cleaned = 词义
        
        # 重新构建表格行，添加词义id列
        new_line = f"| {序号} | {例词} | {读音} | {词义_cleaned} | {词义id} | {例句} | {出处} | {年级} |"
        processed_lines.append(new_line + '\n')
    
    # 写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(processed_lines)
    
    print(f"处理完成！结果已保存到 {output_file}")

def main():
    """
    主函数
    """
    input_file = "初中_年级.md"
    output_file = "初中_词义id.md"
    
    try:
        extract_meaning_id(input_file, output_file)
        print("词义id提取成功！")
        
        # 显示处理统计
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        table_lines = [line for line in lines if line.strip().startswith('|') and line.strip().endswith('|')]
        data_lines = table_lines[2:]  # 去掉表头和分隔线
        
        # 统计有词义id的行数
        meaning_id_count = 0
        for line in data_lines:
            cells = [cell.strip() for cell in line.split('|')[1:-1]]
            if len(cells) >= 8 and cells[4]:  # 词义id列不为空
                meaning_id_count += 1
        
        print(f"共处理了 {len(data_lines)} 行数据")
        print(f"其中 {meaning_id_count} 行包含词义id")
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {input_file}")
    except Exception as e:
        print(f"处理过程中出现错误：{e}")

if __name__ == "__main__":
    main()
