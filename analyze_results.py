#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析文言实词解析结果
"""

import json
from collections import Counter

def analyze_wenyan_words(json_file):
    """分析文言实词JSON文件"""
    
    with open(json_file, 'r', encoding='utf-8') as f:
        words_data = json.load(f)
    
    print(f"=== 文言实词解析结果分析 ===")
    print(f"JSON文件: {json_file}")
    print(f"总词条数: {len(words_data)}")
    
    # 统计用法数量
    usage_counts = []
    part_of_speech_counter = Counter()
    total_usages = 0
    
    valid_words = 0
    for word_data in words_data:
        word = word_data.get("实词", "")
        usages = word_data.get("用法", [])
        
        if word and usages:
            valid_words += 1
            usage_count = len(usages)
            usage_counts.append(usage_count)
            total_usages += usage_count
            
            # 统计词性
            for usage in usages:
                pos = usage.get("词性", "").strip()
                if pos:
                    part_of_speech_counter[pos] += 1
    
    print(f"有效词条数: {valid_words}")
    print(f"总用法数: {total_usages}")
    print(f"平均每词用法数: {total_usages/valid_words:.2f}")
    
    # 用法数量分布
    usage_counter = Counter(usage_counts)
    print(f"\n=== 用法数量分布 ===")
    for count in sorted(usage_counter.keys()):
        print(f"{count}种用法: {usage_counter[count]}个词")
    
    # 词性统计
    print(f"\n=== 词性统计 (前10) ===")
    for pos, count in part_of_speech_counter.most_common(10):
        print(f"{pos}: {count}次")
    
    # 显示一些示例词条
    print(f"\n=== 示例词条 ===")
    for i, word_data in enumerate(words_data[:3]):
        word = word_data.get("实词", "")
        usages = word_data.get("用法", [])
        print(f"\n{i+1}. {word} ({len(usages)}种用法)")
        for j, usage in enumerate(usages[:2]):  # 只显示前2种用法
            pos = usage.get("词性", "")
            meaning = usage.get("含义", "")[:50]  # 截取前50字符
            print(f"   {j+1}) {pos}: {meaning}...")
    
    # 检查数据质量
    print(f"\n=== 数据质量检查 ===")
    empty_word_count = 0
    empty_usage_count = 0
    incomplete_usage_count = 0
    
    for word_data in words_data:
        word = word_data.get("实词", "").strip()
        if not word:
            empty_word_count += 1
            continue
            
        usages = word_data.get("用法", [])
        if not usages:
            empty_usage_count += 1
            continue
            
        for usage in usages:
            pos = usage.get("词性", "").strip()
            meaning = usage.get("含义", "").strip()
            if not pos or not meaning:
                incomplete_usage_count += 1
    
    print(f"空实词名称: {empty_word_count}")
    print(f"无用法词条: {empty_usage_count}")
    print(f"不完整用法: {incomplete_usage_count}")
    
    return {
        'total_words': len(words_data),
        'valid_words': valid_words,
        'total_usages': total_usages,
        'avg_usages': total_usages/valid_words if valid_words > 0 else 0,
        'part_of_speech': dict(part_of_speech_counter),
        'usage_distribution': dict(usage_counter)
    }

def compare_files(file1, file2):
    """比较两个JSON文件的解析结果"""
    print(f"\n=== 文件对比 ===")
    
    result1 = analyze_wenyan_words(file1)
    print(f"\n" + "="*50)
    result2 = analyze_wenyan_words(file2)
    
    print(f"\n=== 对比总结 ===")
    print(f"文件1 ({file1}): {result1['valid_words']}个有效词条, {result1['total_usages']}种用法")
    print(f"文件2 ({file2}): {result2['valid_words']}个有效词条, {result2['total_usages']}种用法")

if __name__ == "__main__":
    # 分析最终结果
    try:
        analyze_wenyan_words("wenyan_words_final.json")
    except FileNotFoundError:
        print("wenyan_words_final.json 文件不存在")

    # 分析修复后的结果
    try:
        print(f"\n" + "="*60)
        analyze_wenyan_words("wenyan_words_fixed.json")
    except FileNotFoundError:
        print("wenyan_words_fixed.json 文件不存在")
