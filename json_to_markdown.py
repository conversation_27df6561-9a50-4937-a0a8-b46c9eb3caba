#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将文言文实词JSON文件转换为Markdown表格格式的脚本
"""

import json
import sys
from pathlib import Path


def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误：JSON文件格式错误 - {e}")
        sys.exit(1)


def convert_to_markdown_table(data):
    """将JSON数据转换为Markdown表格格式"""
    markdown_content = []
    
    # 添加标题
    markdown_content.append("# 文言文实词表\n")
    
    # 表格头部
    table_header = [
        "| 实词 | 用法ID | 词性 | 含义 | 例句ID | 出处 | 内容 | 释义 |",
        "|------|--------|------|------|--------|------|------|------|"
    ]
    markdown_content.extend(table_header)
    
    # 遍历每个实词
    for word_entry in data:
        word = word_entry.get("实词", "")
        
        # 遍历每个用法
        for usage in word_entry.get("用法", []):
            usage_id = usage.get("用法id", "")
            part_of_speech = usage.get("词性", "")
            meaning = usage.get("含义", "")
            
            # 遍历每个例句
            for example in usage.get("例句", []):
                example_id = example.get("例句id", "")
                source = example.get("出处", "")
                content = example.get("内容", "")
                interpretation = example.get("释义", "")
                
                # 清理文本中的管道符号，避免破坏表格格式
                word_clean = word.replace("|", "\\|")
                meaning_clean = meaning.replace("|", "\\|")
                source_clean = source.replace("|", "\\|")
                content_clean = content.replace("|", "\\|")
                interpretation_clean = interpretation.replace("|", "\\|")
                
                # 构建表格行
                row = f"| {word_clean} | {usage_id} | {part_of_speech} | {meaning_clean} | {example_id} | {source_clean} | {content_clean} | {interpretation_clean} |"
                markdown_content.append(row)
    
    return "\n".join(markdown_content)


def save_markdown_file(content, output_path):
    """保存Markdown文件"""
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"成功生成Markdown文件：{output_path}")
    except Exception as e:
        print(f"错误：保存文件失败 - {e}")
        sys.exit(1)


def main():
    """主函数"""
    # 输入和输出文件路径
    input_file = "wenyan_words_restructured.json"
    output_file = "wenyan_words_table.md"
    
    # 检查输入文件是否存在
    if not Path(input_file).exists():
        print(f"错误：输入文件 {input_file} 不存在")
        sys.exit(1)
    
    print(f"正在读取文件：{input_file}")
    
    # 加载JSON数据
    data = load_json_file(input_file)
    
    print(f"成功加载 {len(data)} 个实词条目")
    
    # 转换为Markdown表格
    print("正在转换为Markdown表格格式...")
    markdown_content = convert_to_markdown_table(data)
    
    # 保存Markdown文件
    save_markdown_file(markdown_content, output_file)
    
    # 统计信息
    total_rows = markdown_content.count("|") // 8  # 每行有8个单元格
    print(f"转换完成！共生成 {total_rows - 1} 行数据（不包括表头）")


if __name__ == "__main__":
    main()
