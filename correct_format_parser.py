#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pdfplumber
import re

def extract_pdf_content(pdf_path):
    """提取PDF内容"""
    all_text = ""
    
    with pdfplumber.open(pdf_path) as pdf:
        print(f"PDF总页数: {len(pdf.pages)}")
        
        for page_num, page in enumerate(pdf.pages, 1):
            print(f"正在处理第 {page_num} 页...")
            text = page.extract_text()
            if text:
                all_text += text + "\n"
    
    return all_text

def clean_text(text):
    """清理文本，去掉页眉页脚等"""
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        # 去掉大标题
        if "小笼妈2023年初中实词虚词表" in line:
            continue
            
        # 去掉页眉
        if "睿爸小屋公众号" in line:
            continue
            
        # 去掉页脚
        if re.match(r'^第\s*\d+\s*页\s*$', line):
            continue
            
        # 去掉训练第x天
        if re.match(r'.*训练第\d+天.*', line):
            continue
            
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def parse_correct_format(text):
    """按照正确格式解析所有词条"""
    lines = text.split('\n')
    entries = []
    
    current_word_id = None
    current_word = None
    current_meaning = None
    current_example = None
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # 跳过空行和表头
        if not line or (line.startswith('序号') and '例词' in line):
            i += 1
            continue
        
        # 模式1: 序号 + 例词开头的行
        word_match = re.match(r'^(\d+)\s+(\S+)(?:\s+(.*))?$', line)
        if word_match:
            current_word_id = word_match.group(1)
            current_word = word_match.group(2)
            rest_content = word_match.group(3) if word_match.group(3) else ""
            
            # 重置当前状态
            current_meaning = None
            current_example = None
            
            # 如果同行有其他内容，处理它
            if rest_content:
                meaning, example, source = parse_content_correctly(rest_content)
                if meaning and example and source:
                    entries.append({
                        'id': current_word_id,
                        'word': current_word,
                        'meaning': meaning,
                        'example': example,
                        'source': source
                    })
                else:
                    current_meaning = rest_content
            
            i += 1
            continue
        
        # 模式2: 词义行（以①②③等开头）
        meaning_match = re.match(r'^([①②③④⑤⑥⑦⑧⑨⑩].*)$', line)
        if meaning_match and current_word_id:
            meaning_content = meaning_match.group(1)
            meaning, example, source = parse_content_correctly(meaning_content)
            
            if meaning and example and source:
                entries.append({
                    'id': current_word_id,
                    'word': current_word,
                    'meaning': meaning,
                    'example': example,
                    'source': source
                })
            else:
                current_meaning = meaning_content
                current_example = None
            
            i += 1
            continue
        
        # 模式3: 包含出处的行
        if '《' in line and '》' in line and '（' in line and '）' in line and current_word_id:
            source_match = re.search(r'《([^》]+)》（([^）]+)）', line)
            if source_match:
                source = source_match.group(0)
                before_source = line[:source_match.start()].strip()
                
                # 如果有当前词义，组合成完整条目
                if current_meaning:
                    entries.append({
                        'id': current_word_id,
                        'word': current_word,
                        'meaning': current_meaning,
                        'example': before_source,
                        'source': source
                    })
                    current_meaning = None
                else:
                    # 没有词义，可能是单独的例句
                    entries.append({
                        'id': current_word_id,
                        'word': current_word,
                        'meaning': '',
                        'example': before_source,
                        'source': source
                    })
            
            i += 1
            continue
        
        # 模式4: 其他内容行
        if current_word_id and line:
            # 跳过特殊标记
            if '（非考试篇目）' in line or (line.startswith('（') and line.endswith('）')):
                i += 1
                continue
            
            # 可能是例句内容
            current_example = line
        
        i += 1
    
    return entries

def parse_content_correctly(content):
    """正确解析内容，分离词义、例句和出处"""
    meaning = ""
    example = ""
    source = ""
    
    # 查找出处模式
    source_match = re.search(r'《([^》]+)》（([^）]+)）', content)
    if source_match:
        source = source_match.group(0)
        before_source = content[:source_match.start()].strip()
        
        # 查找词义标记
        meaning_match = re.match(r'^([①②③④⑤⑥⑦⑧⑨⑩][^①②③④⑤⑥⑦⑧⑨⑩]*?)(.*)$', before_source)
        if meaning_match:
            meaning = meaning_match.group(1).strip()
            remaining = meaning_match.group(2).strip()
            if remaining:
                example = remaining
        else:
            # 没有词义标记，整个作为例句
            example = before_source
    else:
        # 没有出处，检查是否有词义标记
        meaning_match = re.match(r'^([①②③④⑤⑥⑦⑧⑨⑩][^①②③④⑤⑥⑦⑧⑨⑩]*)(.*)$', content)
        if meaning_match:
            meaning = meaning_match.group(1).strip()
            remaining = meaning_match.group(2).strip()
            if remaining:
                example = remaining
    
    return meaning, example, source

def create_correct_markdown_table(entries):
    """创建正确格式的markdown表格"""
    markdown_content = "# 中考文言虚实词\n\n"
    markdown_content += "| 序号 | 例词 | 词义 | 例句 | 出处 |\n"
    markdown_content += "|------|------|------|------|------|\n"
    
    for entry in entries:
        # 清理内容
        meaning = entry['meaning'].replace('\n', ' ').strip()
        example = entry['example'].replace('\n', ' ').strip()
        source = entry['source'].replace('\n', ' ').strip()
        
        # 只输出有意义的条目（至少有词义或例句）
        if meaning or example:
            markdown_content += f"| {entry['id']} | {entry['word']} | {meaning} | {example} | {source} |\n"
    
    return markdown_content

def main():
    pdf_path = "中考文言虚实词.pdf"
    
    print("开始提取PDF内容...")
    raw_text = extract_pdf_content(pdf_path)
    
    print("清理文本...")
    cleaned_text = clean_text(raw_text)
    
    # 保存清理后的文本用于调试
    with open("cleaned_correct_format.txt", "w", encoding="utf-8") as f:
        f.write(cleaned_text)
    
    print("按正确格式解析词条...")
    entries = parse_correct_format(cleaned_text)
    
    print(f"共解析出 {len(entries)} 个词条")
    
    print("生成正确格式的markdown表格...")
    markdown_content = create_correct_markdown_table(entries)
    
    # 保存markdown文件
    with open("中考文言虚实词_正确格式.md", "w", encoding="utf-8") as f:
        f.write(markdown_content)
    
    print("转换完成！文件已保存为 中考文言虚实词_正确格式.md")
    
    # 显示前几个词条作为预览
    print("\n前10个词条预览：")
    for i, entry in enumerate(entries[:10]):
        if entry['meaning'] or entry['example']:
            print(f"| {entry['id']} | {entry['word']} | {entry['meaning']} | {entry['example']} | {entry['source']} |")

if __name__ == "__main__":
    main()
