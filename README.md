# 文言实词300个PDF转JSON工具

这个工具可以将文言实词PDF文档转换为结构化的JSON数据格式，方便后续的数据处理和应用开发。

## 功能特点

- 支持从PDF文件提取文言实词内容
- 自动识别实词名称、词性、含义、例句等信息
- 输出标准化的JSON格式数据
- 支持多种PDF文本提取方法
- 提供调试模式便于问题排查

## 输出格式

每个实词的JSON结构如下：

```json
{
  "实词": "昂",
  "用法": [
    {
      "词性": "动词",
      "含义": "仰起，抬起",
      "例句出处": "《核舟记》",
      "例句内容": "袒胸露乳，矫首昂视",
      "例句释义": "（佛印）袒露胸脯露出乳头，抬起头往上看"
    },
    {
      "词性": "动词", 
      "含义": "升高，抬高",
      "例句出处": "《促织》",
      "例句内容": "昂其直，居为奇货",
      "例句释义": "（市中游侠儿）抬高它的价值，囤积起来当做珍奇的货物"
    }
  ]
}
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 基础解析器

```bash
python pdf_to_json_converter.py 文言实词300个最新.pdf wenyan_words.json
```

### 2. 高级解析器（推荐）

```bash
python advanced_wenyan_parser.py 文言实词300个最新.pdf wenyan_words.json
```

### 3. 测试模式（只处理前几个词条）

```bash
python advanced_wenyan_parser.py 文言实词300个最新.pdf test_result.json 5
```

### 4. 运行测试

```bash
python test_parser.py
```

## 文件说明

- `pdf_to_json_converter.py` - 基础PDF解析器
- `advanced_wenyan_parser.py` - 高级解析器，支持更复杂的格式
- `test_parser.py` - 测试脚本
- `requirements.txt` - 依赖包列表
- `README.md` - 使用说明

## 解析规律总结

根据文言实词文档的格式特点，解析器识别以下模式：

### 1. 实词标识
- 数字编号：`1、昂`、`2、安` 等
- 中文编号：`一、昂`、`二、安` 等

### 2. 用法标识
- 圆圈数字：`①`、`②`、`③` 等
- 每个用法包含：词性 + 含义

### 3. 例句格式
- 标识：`例：` 或 `例:`
- 格式：`《出处》例句内容`
- 释义：通常在括号中 `（释义内容）`

### 4. 词性识别
支持识别的词性包括：
- 名词、动词、形容词、副词
- 介词、连词、助词、叹词
- 代词、数词、量词、语气词、象声词

## 注意事项

1. **PDF质量**：确保PDF文件是文本格式而非扫描图片
2. **编码问题**：输出文件使用UTF-8编码
3. **格式变化**：如果PDF格式与预期不符，可能需要调整解析规则
4. **调试模式**：开启调试模式会生成额外的日志文件

## 故障排除

### 1. 无法提取PDF文本
- 检查PDF是否为扫描版（需要OCR）
- 尝试不同的PDF处理库
- 检查文件是否损坏

### 2. 解析结果不准确
- 检查PDF文档的格式是否符合预期
- 调整正则表达式匹配规则
- 开启调试模式查看原始文本

### 3. 依赖安装问题
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

## 扩展功能

可以根据需要添加以下功能：
- OCR支持（处理扫描版PDF）
- 批量处理多个PDF文件
- 数据验证和清理
- 导出为其他格式（Excel、CSV等）
- Web界面

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
