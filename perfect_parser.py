#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pdfplumber
import re

def extract_pdf_content(pdf_path):
    """提取PDF内容"""
    all_text = ""
    
    with pdfplumber.open(pdf_path) as pdf:
        print(f"PDF总页数: {len(pdf.pages)}")
        
        for page_num, page in enumerate(pdf.pages, 1):
            print(f"正在处理第 {page_num} 页...")
            text = page.extract_text()
            if text:
                all_text += text + "\n"
    
    return all_text

def clean_text(text):
    """清理文本，去掉页眉页脚等"""
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        # 去掉大标题
        if "小笼妈2023年初中实词虚词表" in line:
            continue
            
        # 去掉页眉
        if "睿爸小屋公众号" in line:
            continue
            
        # 去掉页脚
        if re.match(r'^第\s*\d+\s*页\s*$', line):
            continue
            
        # 去掉训练第x天
        if re.match(r'.*训练第\d+天.*', line):
            continue
            
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def parse_perfect_entries(text):
    """基于正确格式解析所有词条，确保每行都是完整的条目"""
    lines = text.split('\n')
    entries = []
    
    current_word_id = None
    current_word = None
    pending_content = []
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # 跳过空行和表头
        if not line or (line.startswith('序号') and '例词' in line):
            i += 1
            continue
        
        # 模式1: 序号 + 例词开头的行
        word_match = re.match(r'^(\d+)\s+(\S+)(?:\s+(.*))?$', line)
        if word_match:
            # 处理之前积累的内容
            if current_word_id and current_word and pending_content:
                process_pending_content(current_word_id, current_word, pending_content, entries)
            
            current_word_id = word_match.group(1)
            current_word = word_match.group(2)
            rest_content = word_match.group(3) if word_match.group(3) else ""
            
            pending_content = []
            if rest_content:
                pending_content.append(rest_content)
            
            i += 1
            continue
        
        # 收集当前词条的所有相关内容
        if current_word_id and line:
            # 跳过特殊标记
            if '（非考试篇目）' in line or (line.startswith('（') and line.endswith('）') and len(line) < 20):
                i += 1
                continue
            
            pending_content.append(line)
        
        i += 1
    
    # 处理最后一个词条
    if current_word_id and current_word and pending_content:
        process_pending_content(current_word_id, current_word, pending_content, entries)
    
    return entries

def process_pending_content(word_id, word, content_lines, entries):
    """处理积累的内容，生成完整的条目"""
    # 将所有内容合并
    full_content = ' '.join(content_lines)
    
    # 按出处分割内容
    source_pattern = r'《([^》]+)》（([^）]+)）'
    parts = re.split(source_pattern, full_content)
    
    current_meaning = ""
    current_example = ""
    
    i = 0
    while i < len(parts):
        if i == 0:
            # 第一部分，可能包含词义和例句
            before_first_source = parts[i].strip()
            meaning, example = extract_meaning_and_example(before_first_source)
            current_meaning = meaning
            current_example = example
            
            if i + 2 < len(parts):
                # 有出处信息
                source_name = parts[i + 1]
                source_grade = parts[i + 2]
                source = f"《{source_name}》（{source_grade}）"
                
                # 创建完整条目
                if current_meaning or current_example:
                    entries.append({
                        'id': word_id,
                        'word': word,
                        'meaning': current_meaning,
                        'example': current_example,
                        'source': source
                    })
                
                i += 3
            else:
                # 没有出处信息
                if current_meaning or current_example:
                    entries.append({
                        'id': word_id,
                        'word': word,
                        'meaning': current_meaning,
                        'example': current_example,
                        'source': ''
                    })
                break
        
        elif i % 3 == 0:
            # 这是出处之后的内容
            between_sources = parts[i].strip()
            if between_sources:
                meaning, example = extract_meaning_and_example(between_sources)
                current_meaning = meaning
                current_example = example
            
            if i + 2 < len(parts):
                # 有下一个出处
                source_name = parts[i + 1]
                source_grade = parts[i + 2]
                source = f"《{source_name}》（{source_grade}）"
                
                if current_meaning or current_example:
                    entries.append({
                        'id': word_id,
                        'word': word,
                        'meaning': current_meaning,
                        'example': current_example,
                        'source': source
                    })
                
                i += 3
            else:
                # 没有更多出处
                if current_meaning or current_example:
                    entries.append({
                        'id': word_id,
                        'word': word,
                        'meaning': current_meaning,
                        'example': current_example,
                        'source': ''
                    })
                break
        else:
            i += 1

def extract_meaning_and_example(content):
    """从内容中提取词义和例句"""
    if not content:
        return "", ""
    
    # 查找词义标记（①②③等）
    meaning_match = re.match(r'^([①②③④⑤⑥⑦⑧⑨⑩][^①②③④⑤⑥⑦⑧⑨⑩]*?)(.*)$', content)
    
    if meaning_match:
        meaning = meaning_match.group(1).strip()
        remaining = meaning_match.group(2).strip()
        
        # 进一步分离词义和例句
        # 词义通常在句号、逗号或者明显的例句开始处结束
        meaning_end_patterns = [
            r'^([^。！？]*[。！？])\s*(.*)$',  # 以句号等结尾
            r'^([^，]*，)\s*(.*)$',           # 以逗号结尾
            r'^([^）]*）)\s*(.*)$',           # 以右括号结尾
        ]
        
        for pattern in meaning_end_patterns:
            match = re.match(pattern, remaining)
            if match:
                meaning_part = match.group(1).strip()
                example_part = match.group(2).strip()
                if meaning_part and example_part:
                    meaning = meaning + meaning_part
                    return meaning, example_part
        
        # 如果没有找到明确的分界点，检查是否有明显的例句特征
        if remaining:
            # 如果剩余部分看起来像例句（包含常见的例句特征）
            if any(char in remaining for char in '。！？；，') or len(remaining) > 10:
                return meaning, remaining
            else:
                # 否则合并到词义中
                return meaning + remaining, ""
        else:
            return meaning, ""
    else:
        # 没有词义标记，整个作为例句
        return "", content

def create_perfect_markdown_table(entries):
    """创建完美格式的markdown表格"""
    markdown_content = "# 中考文言虚实词\n\n"
    markdown_content += "| 序号 | 例词 | 词义 | 例句 | 出处 |\n"
    markdown_content += "|------|------|------|------|------|\n"
    
    for entry in entries:
        # 清理内容
        meaning = entry['meaning'].replace('\n', ' ').strip()
        example = entry['example'].replace('\n', ' ').strip()
        source = entry['source'].replace('\n', ' ').strip()
        
        # 只输出有意义的条目
        if meaning or example:
            markdown_content += f"| {entry['id']} | {entry['word']} | {meaning} | {example} | {source} |\n"
    
    return markdown_content

def main():
    pdf_path = "中考文言虚实词.pdf"
    
    print("开始提取PDF内容...")
    raw_text = extract_pdf_content(pdf_path)
    
    print("清理文本...")
    cleaned_text = clean_text(raw_text)
    
    # 保存清理后的文本用于调试
    with open("cleaned_perfect.txt", "w", encoding="utf-8") as f:
        f.write(cleaned_text)
    
    print("按完美格式解析词条...")
    entries = parse_perfect_entries(cleaned_text)
    
    print(f"共解析出 {len(entries)} 个词条")
    
    print("生成完美格式的markdown表格...")
    markdown_content = create_perfect_markdown_table(entries)
    
    # 保存markdown文件
    with open("中考文言虚实词_完美版.md", "w", encoding="utf-8") as f:
        f.write(markdown_content)
    
    print("转换完成！文件已保存为 中考文言虚实词_完美版.md")
    
    # 显示前几个词条作为预览
    print("\n前10个词条预览：")
    for i, entry in enumerate(entries[:10]):
        if entry['meaning'] or entry['example']:
            print(f"| {entry['id']} | {entry['word']} | {entry['meaning']} | {entry['example']} | {entry['source']} |")

if __name__ == "__main__":
    main()
