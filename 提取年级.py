#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从出处列中提取年级信息，创建新的年级列
功能：
1. 从出处列中提取年级信息（如"(八下)"、"(九上)"等）
2. 将提取的年级信息放到新的"年级"列
3. 从原"出处"列中移除年级信息
"""

import re

def extract_grade_info(input_file, output_file):
    """
    从出处列中提取年级信息，创建新的年级列
    """
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 存储处理后的行
    processed_lines = []
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 处理表头
        if i == 0:
            # 添加年级列到表头
            processed_lines.append("| 序号 | 例词 | 读音 | 词义 | 例句 | 出处 | 年级 |\n")
            continue
        elif i == 1:
            # 添加分隔线
            processed_lines.append("|---|---|---|---|---|---|---|\n")
            continue
            
        # 如果不是表格行，直接添加
        if not line.startswith('|') or not line.endswith('|'):
            processed_lines.append(line + '\n')
            continue
            
        # 解析表格行
        cells = [cell.strip() for cell in line.split('|')[1:-1]]  # 去掉首尾空元素
        
        if len(cells) != 6:  # 确保有6列
            processed_lines.append(line + '\n')
            continue
            
        序号, 例词, 读音, 词义, 例句, 出处 = cells
        
        # 使用正则表达式提取年级信息
        # 匹配模式：(七上)、(七下)、(八上)、(八下)、(九上)、(九下)、(六上)、(六下)
        grade_pattern = r'\(([六七八九][上下])\)'
        grade_matches = re.findall(grade_pattern, 出处)
        
        # 提取年级信息
        if grade_matches:
            # 如果有多个年级信息，取第一个
            年级 = f"({grade_matches[0]})"
            # 从出处中移除年级信息
            出处_cleaned = re.sub(grade_pattern, '', 出处).strip()
        else:
            年级 = ""
            出处_cleaned = 出处
        
        # 重新构建表格行，添加年级列
        new_line = f"| {序号} | {例词} | {读音} | {词义} | {例句} | {出处_cleaned} | {年级} |"
        processed_lines.append(new_line + '\n')
    
    # 写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(processed_lines)
    
    print(f"处理完成！结果已保存到 {output_file}")

def main():
    """
    主函数
    """
    input_file = "初中_完善.md"
    output_file = "初中_年级.md"
    
    try:
        extract_grade_info(input_file, output_file)
        print("年级信息提取成功！")
        
        # 显示处理统计
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        table_lines = [line for line in lines if line.strip().startswith('|') and line.strip().endswith('|')]
        data_lines = table_lines[2:]  # 去掉表头和分隔线
        
        # 统计有年级信息的行数
        grade_count = 0
        for line in data_lines:
            cells = [cell.strip() for cell in line.split('|')[1:-1]]
            if len(cells) >= 7 and cells[6]:  # 年级列不为空
                grade_count += 1
        
        print(f"共处理了 {len(data_lines)} 行数据")
        print(f"其中 {grade_count} 行包含年级信息")
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {input_file}")
    except Exception as e:
        print(f"处理过程中出现错误：{e}")

if __name__ == "__main__":
    main()
