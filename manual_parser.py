#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def manual_parse():
    """手动解析已知的词条数据"""
    
    # 基于我对原始文本的观察，手动创建一些示例词条
    entries = [
        {
            'id': '1',
            'word': '安',
            'meanings': ['①安稳'],
            'examples': [
                {
                    'content': '风雨不动安如山。',
                    'source': '茅屋为秋风所破歌',
                    'grade': '八下'
                }
            ]
        },
        {
            'id': '2', 
            'word': '被',
            'meanings': ['①pī通"披"。穿在身上或披在身上'],
            'examples': [
                {
                    'content': '将军身被坚执锐。',
                    'source': '陈涉世家',
                    'grade': '九下'
                }
            ]
        },
        {
            'id': '3',
            'word': '本',
            'meanings': ['①基础，本原'],
            'examples': [
                {
                    'content': '此之谓失其本心。',
                    'source': '鱼我所欲也',
                    'grade': '九下'
                }
            ]
        },
        {
            'id': '4',
            'word': '比',
            'meanings': ['①靠近', '②及，等到（……的时候）', '③比较'],
            'examples': [
                {
                    'content': '其两膝相比者，各隐卷底衣褶中。',
                    'source': '核舟记',
                    'grade': '八下'
                },
                {
                    'content': '比至陈，车六七百乘。',
                    'source': '陈涉世家',
                    'grade': '九下'
                },
                {
                    'content': '心却比，男儿烈。',
                    'source': '满江红·小住京华',
                    'grade': '九下'
                }
            ]
        },
        {
            'id': '5',
            'word': '鄙',
            'meanings': ['①浅陋无知，目光短浅，鄙陋'],
            'examples': [
                {
                    'content': '刿曰："肉食者鄙，未能远谋。"',
                    'source': '曹刿论战',
                    'grade': '九下'
                },
                {
                    'content': '先帝不以臣卑鄙。',
                    'source': '出师表',
                    'grade': '九下'
                }
            ]
        }
    ]
    
    return entries

def read_cleaned_text_and_parse():
    """读取清理后的文本并尝试解析"""
    try:
        with open("cleaned_simple.txt", "r", encoding="utf-8") as f:
            text = f.read()
        
        # 按行分割
        lines = text.split('\n')
        entries = []
        current_entry = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 跳过表头
            if line.startswith('序号') and '例词' in line:
                continue
            
            # 尝试匹配完整的词条行
            # 格式：序号 例词 词义 例句 《出处》（年级）
            match = re.match(r'^(\d+)\s+(\S+)\s+(.+)', line)
            if match:
                # 保存之前的词条
                if current_entry:
                    entries.append(current_entry)
                
                entry_id = match.group(1)
                word = match.group(2)
                rest = match.group(3)
                
                current_entry = {
                    'id': entry_id,
                    'word': word,
                    'meanings': [],
                    'examples': []
                }
                
                # 解析剩余部分
                parse_rest_content(rest, current_entry)
            
            elif current_entry:
                # 继续解析当前词条
                parse_rest_content(line, current_entry)
        
        # 添加最后一个词条
        if current_entry:
            entries.append(current_entry)
            
        return entries
        
    except FileNotFoundError:
        print("未找到清理后的文本文件，使用手动数据")
        return manual_parse()

def parse_rest_content(content, entry):
    """解析内容的剩余部分"""
    # 查找出处模式
    source_pattern = r'《([^》]+)》（([^）]+)）'
    
    # 分割内容
    parts = re.split(source_pattern, content)
    
    if len(parts) >= 4:
        # 有出处信息
        before_source = parts[0].strip()
        source = parts[1]
        grade = parts[2]
        after_source = ''.join(parts[3:]).strip()
        
        # 解析词义和例句
        meaning, example = separate_meaning_and_example(before_source)
        
        if meaning:
            entry['meanings'].append(meaning)
        
        if example or source:
            entry['examples'].append({
                'content': example,
                'source': source,
                'grade': grade
            })
        
        # 处理后续内容
        if after_source:
            parse_rest_content(after_source, entry)
    else:
        # 没有出处信息，可能是词义
        if re.match(r'^[①②③④⑤⑥⑦⑧⑨⑩]', content):
            entry['meanings'].append(content)
        elif content and entry['examples']:
            # 可能是例句内容的延续
            if not entry['examples'][-1]['content']:
                entry['examples'][-1]['content'] = content

def separate_meaning_and_example(text):
    """分离词义和例句"""
    # 查找词义标记
    meaning_match = re.match(r'^([①②③④⑤⑥⑦⑧⑨⑩][^①②③④⑤⑥⑦⑧⑨⑩]*?)(.*)$', text)
    
    if meaning_match:
        meaning = meaning_match.group(1).strip()
        example = meaning_match.group(2).strip()
        return meaning, example
    else:
        # 没有找到词义标记，整个作为例句
        return '', text

def format_to_markdown(entries):
    """格式化为markdown"""
    markdown_content = "# 中考文言虚实词\n\n"
    
    for entry in entries:
        markdown_content += f"## 例词id：{entry['id']}\n"
        markdown_content += f"例词：{entry['word']}\n"
        
        for meaning in entry['meanings']:
            markdown_content += f"词义：{meaning}\n"
        
        for i, example in enumerate(entry['examples'], 1):
            markdown_content += f"例句{i}：\n"
            markdown_content += f"-内容：{example['content']}\n"
            markdown_content += f"-出处：《{example['source']}》\n"
            markdown_content += f"-年级：（{example['grade']}）\n"
        
        markdown_content += "\n"
    
    return markdown_content

def main():
    print("开始解析词条...")
    
    # 尝试从清理后的文本解析，如果失败则使用手动数据
    entries = read_cleaned_text_and_parse()
    
    print(f"共解析出 {len(entries)} 个词条")
    
    print("生成markdown...")
    markdown_content = format_to_markdown(entries)
    
    # 保存markdown文件
    with open("中考文言虚实词_手动版.md", "w", encoding="utf-8") as f:
        f.write(markdown_content)
    
    print("转换完成！文件已保存为 中考文言虚实词_手动版.md")
    
    # 显示前几个词条作为预览
    print("\n前5个词条预览：")
    for i, entry in enumerate(entries[:5]):
        print(f"词条{i+1}: ID={entry['id']}, 词={entry['word']}")
        for meaning in entry['meanings']:
            print(f"  词义: {meaning}")
        for example in entry['examples']:
            print(f"  例句: {example['content']} —— 《{example['source']}》（{example['grade']}）")

if __name__ == "__main__":
    main()
