#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
填充词义id中的空白值
将空白单元格填充为上一个有值的id
"""

import re

def fill_word_id(input_file, output_file):
    """
    读取markdown表格文件，填充词义id列中的空白值
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
    """
    
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到表格开始的位置（跳过表头）
    table_start = 0
    for i, line in enumerate(lines):
        if line.strip().startswith('|---'):
            table_start = i + 1
            break
    
    # 处理表格数据行
    last_word_id = ""  # 记录上一个有值的词义id
    
    for i in range(table_start, len(lines)):
        line = lines[i].strip()
        if not line or not line.startswith('|'):
            continue
            
        # 分割表格列
        columns = [col.strip() for col in line.split('|')]
        if len(columns) < 9:  # 确保有足够的列（包括开头和结尾的空字符串）
            continue
            
        # 词义id在第5列（索引为5，因为第0列是空字符串）
        word_id = columns[5]
        
        if word_id:  # 如果词义id不为空
            last_word_id = word_id
        else:  # 如果词义id为空，填充上一个有值的id
            if last_word_id:
                columns[5] = last_word_id
                # 重新构建这一行
                lines[i] = '| ' + ' | '.join(columns[1:-1]) + ' |\n'
    
    # 写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print(f"处理完成！结果已保存到 {output_file}")

def main():
    """主函数"""
    input_file = "初中_最终.md"
    output_file = "初中_最终_填充后.md"
    
    try:
        fill_word_id(input_file, output_file)
        
        # 统计填充的数量
        with open(input_file, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        with open(output_file, 'r', encoding='utf-8') as f:
            filled_content = f.read()
        
        # 简单统计空白词义id的数量变化
        original_empty = original_content.count('|  |')
        filled_empty = filled_content.count('|  |')
        
        print(f"原文件中词义id列空白数量: {original_empty}")
        print(f"填充后词义id列空白数量: {filled_empty}")
        print(f"成功填充了 {original_empty - filled_empty} 个空白词义id")
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {input_file}")
    except Exception as e:
        print(f"处理过程中出现错误: {e}")

if __name__ == "__main__":
    main()
