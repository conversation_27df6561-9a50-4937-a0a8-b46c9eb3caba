#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pdfplumber
import re

def extract_pdf_content(pdf_path):
    """提取PDF内容"""
    all_text = ""
    
    with pdfplumber.open(pdf_path) as pdf:
        print(f"PDF总页数: {len(pdf.pages)}")
        
        for page_num, page in enumerate(pdf.pages, 1):
            print(f"正在处理第 {page_num} 页...")
            text = page.extract_text()
            if text:
                all_text += text + "\n"
    
    return all_text

def clean_text(text):
    """清理文本，去掉页眉页脚等"""
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        # 去掉大标题
        if "小笼妈2023年初中实词虚词表" in line:
            continue
            
        # 去掉页眉
        if "睿爸小屋公众号" in line:
            continue
            
        # 去掉页脚
        if re.match(r'^第\s*\d+\s*页\s*$', line):
            continue
            
        # 去掉训练第x天
        if re.match(r'.*训练第\d+天.*', line):
            continue
            
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def parse_all_entries(text):
    """基于正确格式解析所有词条"""
    lines = text.split('\n')
    entries = []
    
    current_word_id = None
    current_word = None
    pending_meaning = None
    pending_example = None
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # 跳过空行和表头
        if not line or (line.startswith('序号') and '例词' in line):
            i += 1
            continue
        
        # 模式1: 序号 + 例词开头的行
        word_match = re.match(r'^(\d+)\s+(\S+)(?:\s+(.*))?$', line)
        if word_match:
            # 保存之前的条目
            if current_word_id and current_word and (pending_meaning or pending_example):
                entries.append({
                    'id': current_word_id,
                    'word': current_word,
                    'meaning': pending_meaning or '',
                    'example': pending_example or '',
                    'source': ''
                })
            
            current_word_id = word_match.group(1)
            current_word = word_match.group(2)
            rest_content = word_match.group(3) if word_match.group(3) else ""
            
            pending_meaning = None
            pending_example = None
            
            # 处理同行的其他内容
            if rest_content:
                meaning, example, source = parse_line_content(rest_content)
                if meaning or example or source:
                    entries.append({
                        'id': current_word_id,
                        'word': current_word,
                        'meaning': meaning,
                        'example': example,
                        'source': source
                    })
                else:
                    pending_meaning = rest_content
            
            i += 1
            continue
        
        # 模式2: 词义行（以①②③等开头）
        meaning_match = re.match(r'^([①②③④⑤⑥⑦⑧⑨⑩].*)$', line)
        if meaning_match and current_word_id:
            meaning_content = meaning_match.group(1)
            meaning, example, source = parse_line_content(meaning_content)
            
            if meaning or example or source:
                entries.append({
                    'id': current_word_id,
                    'word': current_word,
                    'meaning': meaning,
                    'example': example,
                    'source': source
                })
            else:
                pending_meaning = meaning_content
            
            i += 1
            continue
        
        # 模式3: 包含出处的行
        if '《' in line and '》' in line and '（' in line and '）' in line:
            source_match = re.search(r'《([^》]+)》（([^）]+)）', line)
            if source_match and current_word_id:
                source = source_match.group(0)
                before_source = line[:source_match.start()].strip()
                
                # 如果有待处理的词义，组合起来
                if pending_meaning:
                    entries.append({
                        'id': current_word_id,
                        'word': current_word,
                        'meaning': pending_meaning,
                        'example': before_source,
                        'source': source
                    })
                    pending_meaning = None
                else:
                    entries.append({
                        'id': current_word_id,
                        'word': current_word,
                        'meaning': '',
                        'example': before_source,
                        'source': source
                    })
            
            i += 1
            continue
        
        # 模式4: 其他内容行
        if current_word_id and line:
            # 跳过特殊标记
            if '（非考试篇目）' in line or line.startswith('（') and line.endswith('）'):
                i += 1
                continue
            
            # 如果有待处理的词义，这可能是例句
            if pending_meaning:
                pending_example = line
            else:
                # 可能是独立的例句
                entries.append({
                    'id': current_word_id,
                    'word': current_word,
                    'meaning': '',
                    'example': line,
                    'source': ''
                })
        
        i += 1
    
    # 处理最后的待处理内容
    if current_word_id and current_word and (pending_meaning or pending_example):
        entries.append({
            'id': current_word_id,
            'word': current_word,
            'meaning': pending_meaning or '',
            'example': pending_example or '',
            'source': ''
        })
    
    return entries

def parse_line_content(content):
    """解析行内容，分离词义、例句和出处"""
    meaning = ""
    example = ""
    source = ""
    
    # 查找出处
    source_match = re.search(r'《([^》]+)》（([^）]+)）', content)
    if source_match:
        source = source_match.group(0)
        before_source = content[:source_match.start()].strip()
        
        # 分离词义和例句
        meaning_match = re.match(r'^([①②③④⑤⑥⑦⑧⑨⑩][^①②③④⑤⑥⑦⑧⑨⑩]*?)(.*)$', before_source)
        if meaning_match:
            meaning = meaning_match.group(1).strip()
            remaining = meaning_match.group(2).strip()
            if remaining:
                example = remaining
        else:
            # 没有词义标记，整个作为例句
            example = before_source
    else:
        # 没有出处，检查是否有词义标记
        meaning_match = re.match(r'^([①②③④⑤⑥⑦⑧⑨⑩][^①②③④⑤⑥⑦⑧⑨⑩]*)(.*)$', content)
        if meaning_match:
            meaning = meaning_match.group(1).strip()
            remaining = meaning_match.group(2).strip()
            if remaining:
                example = remaining
        else:
            # 整个作为例句或其他内容
            example = content
    
    return meaning, example, source

def create_markdown_table(entries):
    """创建markdown表格"""
    markdown_content = "# 中考文言虚实词\n\n"
    markdown_content += "| 序号 | 例词 | 词义 | 例句 | 出处 |\n"
    markdown_content += "|------|------|------|------|------|\n"
    
    for entry in entries:
        # 清理内容
        meaning = entry['meaning'].replace('\n', ' ').strip()
        example = entry['example'].replace('\n', ' ').strip()
        source = entry['source'].replace('\n', ' ').strip()
        
        # 只输出有实际内容的条目
        if meaning or example or source:
            markdown_content += f"| {entry['id']} | {entry['word']} | {meaning} | {example} | {source} |\n"
    
    return markdown_content

def main():
    pdf_path = "中考文言虚实词.pdf"
    
    print("开始提取PDF内容...")
    raw_text = extract_pdf_content(pdf_path)
    
    print("清理文本...")
    cleaned_text = clean_text(raw_text)
    
    # 保存清理后的文本用于调试
    with open("cleaned_final.txt", "w", encoding="utf-8") as f:
        f.write(cleaned_text)
    
    print("解析所有词条...")
    entries = parse_all_entries(cleaned_text)
    
    print(f"共解析出 {len(entries)} 个词条")
    
    print("生成markdown表格...")
    markdown_content = create_markdown_table(entries)
    
    # 保存markdown文件
    with open("中考文言虚实词_完整版.md", "w", encoding="utf-8") as f:
        f.write(markdown_content)
    
    print("转换完成！文件已保存为 中考文言虚实词_完整版.md")
    
    # 显示统计信息
    word_count = {}
    for entry in entries:
        if entry['word'] in word_count:
            word_count[entry['word']] += 1
        else:
            word_count[entry['word']] = 1
    
    print(f"\n词条统计：共 {len(word_count)} 个不同的词")
    print("前10个词及其条目数：")
    sorted_words = sorted(word_count.items(), key=lambda x: int(x[0]) if x[0].isdigit() else 999)
    for word, count in sorted_words[:10]:
        print(f"  {word}: {count} 个条目")

if __name__ == "__main__":
    main()
