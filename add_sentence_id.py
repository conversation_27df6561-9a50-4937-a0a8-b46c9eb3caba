#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为初中文言文实词表添加例句id列的脚本

规则：
- 当某个例词的词义未出现重复时，请标注为1
- 当某个例词的词义出现重复时，请按1、2、3的递增序号逐渐递增
"""

import pandas as pd
from collections import defaultdict

def add_example_id(input_file, output_file):
    """
    为markdown表格添加例句id列
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
    """
    
    # 读取markdown文件
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到表格开始位置
    table_start = -1
    for i, line in enumerate(lines):
        if line.strip().startswith('| 序号'):
            table_start = i
            break
    
    if table_start == -1:
        print("未找到表格开始位置")
        return
    
    # 解析表格数据
    data_rows = []
    header_line = lines[table_start].strip()
    
    # 解析表头
    headers = [h.strip() for h in header_line.split('|')[1:-1]]  # 去掉首尾空元素
    print(f"表头: {headers}")
    
    # 解析数据行
    for i in range(table_start + 2, len(lines)):
        line = lines[i].strip()
        if not line or not line.startswith('|'):
            break
        
        # 分割行数据
        row_data = [cell.strip() for cell in line.split('|')[1:-1]]  # 去掉首尾空元素
        if len(row_data) == len(headers):
            data_rows.append(row_data)
    
    print(f"共解析到 {len(data_rows)} 行数据")
    
    # 创建DataFrame
    df = pd.DataFrame(data_rows, columns=headers)
    
    # 统计每个例词的词义出现次数
    word_meaning_current_id = defaultdict(lambda: defaultdict(int))
    
    # 添加例句id列
    example_ids = []
    
    for _, row in df.iterrows():
        word = row['例词']
        meaning = row['词义']
        
        # 为当前词义分配id
        word_meaning_current_id[word][meaning] += 1
        example_id = word_meaning_current_id[word][meaning]
        example_ids.append(example_id)
    
    # 添加例句id列到DataFrame
    df['例句id'] = example_ids
    
    # 重新排列列的顺序，将例句id放在词义id之后
    columns_order = ['序号', '例词', '读音', '词义', '词义id', '例句id', '例句', '出处', '年级']
    df = df[columns_order]
    
    # 写入新的markdown文件
    with open(output_file, 'w', encoding='utf-8') as f:
        # 写入表头
        header_line = '| ' + ' | '.join(columns_order) + ' |'
        f.write(header_line + '\n')
        
        # 写入分隔线
        separator_line = '|' + '---|' * len(columns_order)
        f.write(separator_line + '\n')
        
        # 写入数据行
        for _, row in df.iterrows():
            data_line = '| ' + ' | '.join(str(row[col]) for col in columns_order) + ' |'
            f.write(data_line + '\n')
    
    print(f"处理完成！结果已保存到 {output_file}")
    
    # 打印一些统计信息
    print("\n统计信息：")
    word_stats = defaultdict(int)
    for word in word_meaning_current_id:
        for meaning in word_meaning_current_id[word]:
            max_id = word_meaning_current_id[word][meaning]
            word_stats[word] = max(word_stats[word], max_id)
    
    # 显示有重复词义的例词
    repeated_words = {word: count for word, count in word_stats.items() if count > 1}
    if repeated_words:
        print("有重复词义的例词：")
        for word, max_count in sorted(repeated_words.items()):
            print(f"  {word}: 最大例句id = {max_count}")
    else:
        print("没有发现重复词义的例词")

if __name__ == "__main__":
    input_file = "初中_最终_填充后.md"
    output_file = "初中_最终_填充后_带例句id.md"
    
    add_example_id(input_file, output_file)
