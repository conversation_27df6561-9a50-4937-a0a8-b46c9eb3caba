#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动补充文言实词中缺失的词性
根据含义和例句内容智能推断词性
"""

import json
import re

class POSFiller:
    """词性自动填充器"""
    
    def __init__(self):
        # 词性推断规则
        self.pos_rules = {
            '名词': [
                r'本义.*名',
                r'指.*人',
                r'代指',
                r'.*的人',
                r'.*者',
                r'.*物',
                r'.*事',
                r'.*地方',
                r'.*时候',
                r'.*东西',
                r'书名',
                r'《.*》',
                r'敬辞.*称',
                r'谦辞.*称',
            ],
            '动词': [
                r'使.*',
                r'让.*',
                r'令.*',
                r'致使',
                r'导致',
                r'.*行为',
                r'.*动作',
                r'进行.*',
                r'实施.*',
                r'执行.*',
            ],
            '形容词': [
                r'.*的样子',
                r'.*状态',
                r'形容.*',
                r'表示.*的',
                r'.*貌',
                r'.*然',
            ],
            '副词': [
                r'表示.*地',
                r'.*地',
                r'程度.*',
                r'时间.*',
                r'范围.*',
                r'语气.*',
                r'表示语气',
            ],
            '介词': [
                r'引出.*',
                r'表示.*关系',
                r'连接.*',
            ],
            '连词': [
                r'连接.*',
                r'表示.*关系',
                r'转折',
                r'因果',
                r'并列',
            ],
            '助词': [
                r'语气助词',
                r'结构助词',
                r'时态助词',
                r'表示.*语气',
            ],
            '叹词': [
                r'感叹',
                r'惊叹',
                r'表示.*情感',
            ]
        }
        
        # 特殊词汇的词性映射
        self.special_words = {
            '敬辞': '名词',
            '谦辞': '名词', 
            '《论语》': '名词',
            '进士': '名词',
            '人才济济': '形容词',
            '登峰造极': '动词',
        }
    
    def infer_pos(self, meaning, example_content="", example_explanation=""):
        """根据含义和例句推断词性"""
        
        # 检查特殊词汇
        for word, pos in self.special_words.items():
            if word in meaning:
                return pos
        
        # 根据含义推断
        for pos, patterns in self.pos_rules.items():
            for pattern in patterns:
                if re.search(pattern, meaning):
                    return pos
        
        # 根据例句内容推断
        if example_content:
            # 如果例句中包含书名号，通常是名词
            if '《' in example_content and '》' in example_content:
                return '名词'
        
        # 根据例句释义推断
        if example_explanation:
            if '的人' in example_explanation or '者' in example_explanation:
                return '名词'
            if '地' in example_explanation and len(example_explanation) > 10:
                return '副词'
        
        # 默认返回空，需要人工判断
        return ""
    
    def find_missing_pos(self, json_file):
        """找出所有词性为空的用法"""
        with open(json_file, 'r', encoding='utf-8') as f:
            words_data = json.load(f)
        
        missing_pos = []
        
        for word_data in words_data:
            word_id = word_data.get('id')
            word_name = word_data.get('实词', '')
            
            for usage in word_data.get('用法', []):
                usage_id = usage.get('用法id')
                pos = usage.get('词性', '').strip()
                
                if not pos:
                    missing_pos.append({
                        'word_id': word_id,
                        'word_name': word_name,
                        'usage_id': usage_id,
                        'meaning': usage.get('含义', ''),
                        'example_content': usage.get('例句内容', ''),
                        'example_explanation': usage.get('例句释义', ''),
                        'usage_data': usage
                    })
        
        return missing_pos
    
    def suggest_pos_for_missing(self, json_file):
        """为缺失的词性提供建议"""
        missing_pos = self.find_missing_pos(json_file)
        
        print(f"=== 发现 {len(missing_pos)} 个词性缺失的用法 ===\n")
        
        suggestions = []
        
        for i, item in enumerate(missing_pos):
            word_id = item['word_id']
            word_name = item['word_name']
            usage_id = item['usage_id']
            meaning = item['meaning']
            example_content = item['example_content']
            example_explanation = item['example_explanation']
            
            # 推断词性
            suggested_pos = self.infer_pos(meaning, example_content, example_explanation)
            
            print(f"{i+1}. 词条{word_id}（{word_name}）用法{usage_id}")
            print(f"   含义: {meaning[:50]}...")
            print(f"   例句: {example_content[:30]}...")
            print(f"   释义: {example_explanation[:30]}...")
            print(f"   建议词性: {suggested_pos if suggested_pos else '需要人工判断'}")
            print()
            
            suggestions.append({
                'word_id': word_id,
                'usage_id': usage_id,
                'suggested_pos': suggested_pos,
                'item': item
            })
        
        return suggestions
    
    def auto_fill_pos(self, json_file, output_file=None):
        """自动填充词性"""
        with open(json_file, 'r', encoding='utf-8') as f:
            words_data = json.load(f)
        
        suggestions = self.suggest_pos_for_missing(json_file)
        filled_count = 0
        
        # 应用建议的词性
        for suggestion in suggestions:
            if suggestion['suggested_pos']:
                word_id = suggestion['word_id']
                usage_id = suggestion['usage_id']
                suggested_pos = suggestion['suggested_pos']
                
                # 找到对应的词条和用法
                for word_data in words_data:
                    if word_data.get('id') == word_id:
                        for usage in word_data.get('用法', []):
                            if usage.get('用法id') == usage_id:
                                usage['词性'] = suggested_pos
                                filled_count += 1
                                print(f"已填充: 词条{word_id} 用法{usage_id} -> {suggested_pos}")
                                break
                        break
        
        # 保存结果
        if not output_file:
            output_file = "wenyan_words_pos_filled.json"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(words_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n自动填充完成！")
        print(f"已填充: {filled_count} 个词性")
        print(f"结果保存到: {output_file}")
        
        # 检查剩余未填充的
        remaining = self.find_missing_pos(output_file)
        print(f"剩余未填充: {len(remaining)} 个")
        
        return output_file

def main():
    filler = POSFiller()
    
    # 分析缺失的词性
    print("=== 分析词性缺失情况 ===")
    suggestions = filler.suggest_pos_for_missing("wenyan_words_with_usage_ids.json")
    
    # 自动填充
    print("\n=== 开始自动填充 ===")
    output_file = filler.auto_fill_pos("wenyan_words_with_usage_ids.json")
    
    return output_file

if __name__ == "__main__":
    main()
