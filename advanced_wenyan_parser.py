#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级文言实词解析器
支持更复杂的文档格式和更精确的解析
"""

import json
import re
import sys
from typing import List, Dict, Any, Optional
import PyPDF2
import pdfplumber
from pathlib import Path

class AdvancedWenyanParser:
    """高级文言实词解析器"""
    
    def __init__(self):
        self.words_data = []
        self.debug_mode = True
        
        # 定义常见的词性
        self.part_of_speech = [
            '名词', '动词', '形容词', '副词', '介词', '连词', '助词', '叹词',
            '代词', '数词', '量词', '语气词', '象声词'
        ]
        
        # 定义圆圈数字映射
        self.circle_numbers = {
            '①': 1, '②': 2, '③': 3, '④': 4, '⑤': 5,
            '⑥': 6, '⑦': 7, '⑧': 8, '⑨': 9, '⑩': 10
        }
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """从PDF提取文本内容"""
        text = ""
        
        # 优先使用pdfplumber，它对中文支持更好
        try:
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
                        if self.debug_mode:
                            print(f"第{page_num+1}页提取了{len(page_text)}字符")
            
            if text:
                print(f"使用pdfplumber成功提取文本，共{len(text)}字符")
                return text
        except Exception as e:
            print(f"pdfplumber提取失败: {e}")
        
        # 备用方案：PyPDF2
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
                        if self.debug_mode:
                            print(f"第{page_num+1}页提取了{len(page_text)}字符")
            
            if text:
                print(f"使用PyPDF2成功提取文本，共{len(text)}字符")
                return text
        except Exception as e:
            print(f"PyPDF2提取失败: {e}")
        
        return ""
    
    def clean_text(self, text: str) -> str:
        """清理文本，去除多余的空白和格式字符"""
        # 去除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        # 去除页眉页脚等无关内容
        text = re.sub(r'第\s*\d+\s*页', '', text)
        # 标准化标点符号
        text = text.replace('。', '。\n')
        text = text.replace('；', '；\n')
        
        return text.strip()
    
    def split_into_word_entries(self, text: str) -> List[str]:
        """将文本分割为单个实词条目"""
        # 清理文本
        text = self.clean_text(text)
        
        # 多种分割模式
        patterns = [
            r'\n(?=\d+[、．]\s*[^\d])',  # 数字编号
            r'\n(?=[一二三四五六七八九十百][、．]\s*[^\d])',  # 中文数字编号
            r'\n(?=[①②③④⑤⑥⑦⑧⑨⑩])',  # 圆圈数字（用于子条目）
        ]
        
        entries = []
        for pattern in patterns:
            if not entries:  # 只有第一次分割成功才使用
                temp_entries = re.split(pattern, text)
                if len(temp_entries) > 10:  # 如果分割出很多条目，说明这个模式有效
                    entries = temp_entries
                    break
        
        if not entries:
            # 如果所有模式都失败，按段落分割
            entries = text.split('\n\n')
        
        # 过滤和清理条目
        cleaned_entries = []
        for entry in entries:
            entry = entry.strip()
            if entry and len(entry) > 10:  # 过滤太短的条目
                cleaned_entries.append(entry)
        
        print(f"分割得到 {len(cleaned_entries)} 个词条")
        return cleaned_entries
    
    def extract_word_name(self, text: str) -> str:
        """提取实词名称"""
        lines = text.split('\n')
        first_line = lines[0].strip()
        
        # 匹配各种编号格式
        patterns = [
            r'^\d+[、．]\s*(.+)',  # 1、昂
            r'^[一二三四五六七八九十百][、．]\s*(.+)',  # 一、昂
            r'^(.+?)(?:\s|$)',  # 直接取第一个词
        ]
        
        for pattern in patterns:
            match = re.match(pattern, first_line)
            if match:
                word = match.group(1).strip()
                # 去除可能的标点和空格
                word = re.sub(r'[^\u4e00-\u9fff]', '', word)  # 只保留中文字符
                if word:
                    return word
        
        return ""
    
    def parse_usage_entries(self, text: str) -> List[Dict[str, str]]:
        """解析用法条目"""
        usages = []
        
        # 按圆圈数字分割用法
        usage_pattern = r'([①②③④⑤⑥⑦⑧⑨⑩])\s*([^①②③④⑤⑥⑦⑧⑨⑩]+)'
        usage_matches = re.findall(usage_pattern, text)
        
        for circle_num, usage_text in usage_matches:
            usage = self.parse_single_usage(usage_text.strip())
            if usage:
                usages.append(usage)
        
        return usages
    
    def parse_single_usage(self, usage_text: str) -> Optional[Dict[str, str]]:
        """解析单个用法"""
        usage = {
            "词性": "",
            "含义": "",
            "例句出处": "",
            "例句内容": "",
            "例句释义": ""
        }
        
        lines = usage_text.split('\n')
        
        # 解析第一行：词性和含义
        first_line = lines[0].strip()
        
        # 尝试识别词性
        for pos in self.part_of_speech:
            if first_line.startswith(pos):
                usage["词性"] = pos
                # 去掉词性后的内容作为含义
                remaining = first_line[len(pos):].strip()
                if remaining.startswith('，') or remaining.startswith(','):
                    remaining = remaining[1:].strip()
                usage["含义"] = remaining
                break
        
        if not usage["词性"]:
            # 如果没有识别到词性，整行作为含义
            usage["含义"] = first_line
        
        # 解析例句
        example_found = False
        for line in lines[1:]:
            line = line.strip()
            if not line:
                continue
            
            # 匹配例句标记
            if line.startswith('例：') or line.startswith('例:'):
                example_text = line[2:].strip()
                self.parse_example_sentence(usage, example_text)
                example_found = True
            elif example_found and ('（' in line or '）' in line):
                # 例句释义
                usage["例句释义"] = line.strip('（）')
            elif not example_found and ('《' in line):
                # 直接的例句
                self.parse_example_sentence(usage, line)
                example_found = True
        
        return usage if usage["含义"] else None
    
    def parse_example_sentence(self, usage: Dict[str, str], example_text: str):
        """解析例句，提取出处和内容"""
        # 匹配书名号格式：《核舟记》袒胸露乳，矫首昂视
        book_match = re.search(r'《([^》]+)》(.+)', example_text)
        if book_match:
            usage["例句出处"] = f"《{book_match.group(1)}》"
            usage["例句内容"] = book_match.group(2).strip()
        else:
            # 没有书名号，整个作为例句内容
            usage["例句内容"] = example_text.strip()
    
    def parse_word_entry(self, word_text: str) -> Dict[str, Any]:
        """解析单个实词条目"""
        word_data = {
            "实词": "",
            "用法": []
        }
        
        # 提取实词名称
        word_name = self.extract_word_name(word_text)
        if not word_name:
            return word_data
        
        word_data["实词"] = word_name
        
        # 解析用法
        usages = self.parse_usage_entries(word_text)
        word_data["用法"] = usages
        
        return word_data
    
    def parse_pdf_to_json(self, pdf_path: str, output_path: str = None, max_words: int = None):
        """主函数：解析PDF并转换为JSON"""
        print(f"开始处理PDF文件: {pdf_path}")
        
        # 提取PDF文本
        text = self.extract_text_from_pdf(pdf_path)
        if not text:
            print("无法从PDF提取文本内容")
            return
        
        # 保存原始文本用于调试
        if self.debug_mode:
            with open("debug_extracted_text.txt", 'w', encoding='utf-8') as f:
                f.write(text)
            print("原始提取文本已保存到 debug_extracted_text.txt")
        
        # 分割为词条
        word_entries = self.split_into_word_entries(text)
        
        # 限制处理数量（用于测试）
        if max_words:
            word_entries = word_entries[:max_words]
        
        # 解析每个词条
        for i, entry in enumerate(word_entries):
            print(f"\n处理第 {i+1} 个词条:")
            if self.debug_mode:
                print(f"原文: {entry[:200]}...")
            
            word_data = self.parse_word_entry(entry)
            if word_data["实词"]:
                self.words_data.append(word_data)
                print(f"解析结果: {word_data['实词']} - {len(word_data['用法'])}种用法")
            else:
                print("跳过：无法识别的词条格式")
        
        # 输出JSON
        if not output_path:
            output_path = "wenyan_words_advanced.json"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.words_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n解析完成！共处理 {len(self.words_data)} 个实词")
        print(f"结果已保存到: {output_path}")
        
        # 显示示例
        if self.words_data:
            print(f"\n示例数据:")
            print(json.dumps(self.words_data[0], ensure_ascii=False, indent=2))
        
        return self.words_data

def main():
    """主程序入口"""
    if len(sys.argv) < 2:
        print("使用方法: python advanced_wenyan_parser.py <PDF文件路径> [输出JSON文件路径] [最大词数]")
        print("示例: python advanced_wenyan_parser.py 文言实词300个最新.pdf wenyan_words.json 10")
        return
    
    pdf_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    max_words = int(sys.argv[3]) if len(sys.argv) > 3 else None
    
    if not Path(pdf_path).exists():
        print(f"错误: PDF文件不存在: {pdf_path}")
        return
    
    parser = AdvancedWenyanParser()
    parser.parse_pdf_to_json(pdf_path, output_path, max_words)

if __name__ == "__main__":
    main()
