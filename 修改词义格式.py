#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修改词义格式的脚本
功能：
1. 将词义id的圆圈序号改成正常数字（①→1，②→2等）
2. 处理词义中的括号：如果括号前面没有文字，去掉括号；如果前面有文字，保留括号
"""

import re

def process_meaning_format(input_file, output_file):
    """
    修改词义格式
    """
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 圆圈数字到普通数字的映射
    circle_to_number = {
        '①': '1', '②': '2', '③': '3', '④': '4', '⑤': '5',
        '⑥': '6', '⑦': '7', '⑧': '8', '⑨': '9', '⑩': '10',
        '⑪': '11', '⑫': '12', '⑬': '13', '⑭': '14', '⑮': '15',
        '⑯': '16', '⑰': '17', '⑱': '18', '⑲': '19', '⑳': '20'
    }
    
    # 存储处理后的行
    processed_lines = []
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 跳过表头和分隔线
        if i <= 1:
            processed_lines.append(line + '\n')
            continue
            
        # 如果不是表格行，直接添加
        if not line.startswith('|') or not line.endswith('|'):
            processed_lines.append(line + '\n')
            continue
            
        # 解析表格行
        cells = [cell.strip() for cell in line.split('|')[1:-1]]  # 去掉首尾空元素
        
        if len(cells) != 8:  # 确保有8列
            processed_lines.append(line + '\n')
            continue
            
        序号, 例词, 读音, 词义, 词义id, 例句, 出处, 年级 = cells
        
        # 处理词义id：将圆圈数字转换为普通数字
        if 词义id in circle_to_number:
            词义id = circle_to_number[词义id]
        
        # 处理词义中的括号
        # 如果词义以括号开头且括号前没有文字，去掉括号
        if 词义.startswith('(') and ')' in 词义:
            # 检查是否整个词义都在括号内
            if 词义.endswith(')'):
                # 去掉首尾括号
                词义 = 词义[1:-1]
            else:
                # 只去掉开头的括号，找到对应的结束括号
                bracket_count = 0
                end_pos = -1
                for j, char in enumerate(词义):
                    if char == '(':
                        bracket_count += 1
                    elif char == ')':
                        bracket_count -= 1
                        if bracket_count == 0:
                            end_pos = j
                            break
                
                if end_pos > 0:
                    # 去掉开头的括号部分
                    词义 = 词义[1:end_pos] + 词义[end_pos+1:]
        
        # 重新构建表格行
        new_line = f"| {序号} | {例词} | {读音} | {词义} | {词义id} | {例句} | {出处} | {年级} |"
        processed_lines.append(new_line + '\n')
    
    # 写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(processed_lines)
    
    print(f"处理完成！结果已保存到 {output_file}")

def main():
    """
    主函数
    """
    input_file = "初中_词义id.md"
    output_file = "初中_最终.md"
    
    try:
        process_meaning_format(input_file, output_file)
        print("词义格式修改成功！")
        
        # 显示处理统计
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        table_lines = [line for line in lines if line.strip().startswith('|') and line.strip().endswith('|')]
        data_lines = table_lines[2:]  # 去掉表头和分隔线
        
        # 统计有词义id的行数
        meaning_id_count = 0
        bracket_processed_count = 0
        
        for line in data_lines:
            cells = [cell.strip() for cell in line.split('|')[1:-1]]
            if len(cells) >= 8:
                if cells[4]:  # 词义id列不为空
                    meaning_id_count += 1
                # 检查是否处理了括号（这里简单统计包含逗号的词义，表示可能处理过括号）
                if '，' in cells[3] and not cells[3].startswith('('):
                    bracket_processed_count += 1
        
        print(f"共处理了 {len(data_lines)} 行数据")
        print(f"其中 {meaning_id_count} 行包含词义id")
        print(f"处理了括号格式的词义约 {bracket_processed_count} 行")
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {input_file}")
    except Exception as e:
        print(f"处理过程中出现错误：{e}")

if __name__ == "__main__":
    main()
