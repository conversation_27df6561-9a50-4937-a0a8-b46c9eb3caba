#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文言实词300个PDF转JSON工具
将文言实词PDF文档转换为结构化的JSON数据
"""

import json
import re
import sys
from typing import List, Dict, Any
import PyPDF2
import pdfplumber
from pathlib import Path

class WenyanWordParser:
    """文言实词解析器"""
    
    def __init__(self):
        self.words_data = []
        
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """从PDF提取文本内容"""
        text = ""
        
        # 尝试使用pdfplumber提取文本
        try:
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
            print(f"使用pdfplumber成功提取文本，共{len(text)}字符")
            return text
        except Exception as e:
            print(f"pdfplumber提取失败: {e}")
        
        # 备用方案：使用PyPDF2
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            print(f"使用PyPDF2成功提取文本，共{len(text)}字符")
            return text
        except Exception as e:
            print(f"PyPDF2提取失败: {e}")
            return ""
    
    def parse_word_entry(self, word_text: str) -> Dict[str, Any]:
        """解析单个实词条目"""
        word_data = {
            "实词": "",
            "用法": []
        }
        
        lines = word_text.strip().split('\n')
        if not lines:
            return word_data
            
        # 提取实词名称（通常在第一行）
        first_line = lines[0].strip()
        # 匹配数字开头的实词，如 "1、昂"
        word_match = re.match(r'^\d+[、．]\s*(.+)', first_line)
        if word_match:
            word_data["实词"] = word_match.group(1).strip()
        else:
            # 如果没有数字开头，直接取第一行
            word_data["实词"] = first_line
        
        # 解析用法
        current_usage = None
        for line in lines[1:]:
            line = line.strip()
            if not line:
                continue
                
            # 匹配用法编号，如 "① 动词，仰起，抬起"
            usage_match = re.match(r'^[①②③④⑤⑥⑦⑧⑨⑩]\s*(.+)', line)
            if usage_match:
                # 保存上一个用法
                if current_usage:
                    word_data["用法"].append(current_usage)
                
                # 开始新用法
                usage_content = usage_match.group(1)
                current_usage = self.parse_usage_line(usage_content)
                
            elif line.startswith('例：') or line.startswith('例:'):
                # 处理例句
                if current_usage:
                    example_text = line[2:].strip()  # 去掉"例："
                    self.parse_example(current_usage, example_text)
                    
            elif current_usage and ('《' in line or '）' in line):
                # 可能是例句的释义部分
                if '释义' not in current_usage:
                    current_usage['例句释义'] = line
        
        # 添加最后一个用法
        if current_usage:
            word_data["用法"].append(current_usage)
            
        return word_data
    
    def parse_usage_line(self, usage_content: str) -> Dict[str, str]:
        """解析用法行，提取词性和含义"""
        usage = {
            "词性": "",
            "含义": "",
            "例句出处": "",
            "例句内容": "",
            "例句释义": ""
        }
        
        # 尝试分离词性和含义，如 "动词，仰起，抬起"
        if '，' in usage_content:
            parts = usage_content.split('，', 1)
            usage["词性"] = parts[0].strip()
            usage["含义"] = parts[1].strip()
        else:
            # 如果没有逗号分隔，整个作为含义
            usage["含义"] = usage_content.strip()
            
        return usage
    
    def parse_example(self, usage: Dict[str, str], example_text: str):
        """解析例句，提取出处和内容"""
        # 匹配格式：《核舟记》袒胸露乳，矫首昂视
        book_match = re.match(r'《([^》]+)》(.+)', example_text)
        if book_match:
            usage["例句出处"] = f"《{book_match.group(1)}》"
            usage["例句内容"] = book_match.group(2).strip()
        else:
            # 如果没有书名号，整个作为例句内容
            usage["例句内容"] = example_text
    
    def split_into_word_entries(self, text: str) -> List[str]:
        """将文本分割为单个实词条目"""
        # 使用数字编号分割，如 "1、" "2、" 等
        pattern = r'\n(?=\d+[、．]\s*\S)'
        entries = re.split(pattern, text)
        
        # 过滤空条目
        entries = [entry.strip() for entry in entries if entry.strip()]
        
        print(f"分割得到 {len(entries)} 个词条")
        return entries
    
    def parse_pdf_to_json(self, pdf_path: str, output_path: str = None):
        """主函数：解析PDF并转换为JSON"""
        print(f"开始处理PDF文件: {pdf_path}")
        
        # 提取PDF文本
        text = self.extract_text_from_pdf(pdf_path)
        if not text:
            print("无法从PDF提取文本内容")
            return
        
        # 分割为词条
        word_entries = self.split_into_word_entries(text)
        
        # 解析每个词条
        for i, entry in enumerate(word_entries[:5]):  # 先处理前5个测试
            print(f"\n处理第 {i+1} 个词条:")
            print(f"原文: {entry[:100]}...")
            
            word_data = self.parse_word_entry(entry)
            if word_data["实词"]:
                self.words_data.append(word_data)
                print(f"解析结果: {word_data['实词']} - {len(word_data['用法'])}种用法")
        
        # 输出JSON
        if not output_path:
            output_path = "wenyan_words.json"
            
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.words_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n解析完成！共处理 {len(self.words_data)} 个实词")
        print(f"结果已保存到: {output_path}")
        
        # 显示示例
        if self.words_data:
            print(f"\n示例数据:")
            print(json.dumps(self.words_data[0], ensure_ascii=False, indent=2))

def main():
    """主程序入口"""
    if len(sys.argv) < 2:
        print("使用方法: python pdf_to_json_converter.py <PDF文件路径> [输出JSON文件路径]")
        print("示例: python pdf_to_json_converter.py 文言实词300个最新.pdf wenyan_words.json")
        return
    
    pdf_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not Path(pdf_path).exists():
        print(f"错误: PDF文件不存在: {pdf_path}")
        return
    
    parser = WenyanWordParser()
    parser.parse_pdf_to_json(pdf_path, output_path)

if __name__ == "__main__":
    main()
