#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为文言实词JSON数据添加用法ID序号
每个实词的用法从1开始编号
"""

import json

def add_usage_ids(json_file, output_file=None):
    """为每个用法添加ID序号"""
    
    # 读取原始数据
    with open(json_file, 'r', encoding='utf-8') as f:
        words_data = json.load(f)
    
    print(f"开始处理 {len(words_data)} 个实词...")
    
    # 为每个实词的用法添加ID
    for word_data in words_data:
        word_name = word_data.get('实词', '')
        usages = word_data.get('用法', [])
        
        # 为每个用法添加用法ID
        for i, usage in enumerate(usages):
            usage_id = i + 1  # 从1开始编号
            # 在字典开头插入用法ID
            new_usage = {"用法id": usage_id}
            new_usage.update(usage)
            usages[i] = new_usage
        
        print(f"处理完成: {word_name} - {len(usages)}种用法")
    
    # 保存结果
    if not output_file:
        output_file = "wenyan_words_with_usage_ids.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(words_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n处理完成！结果已保存到: {output_file}")
    
    # 显示示例
    if words_data:
        print(f"\n示例数据:")
        sample_word = words_data[0]
        print(f"实词: {sample_word['实词']}")
        for usage in sample_word['用法'][:2]:  # 显示前2个用法
            print(f"  用法{usage['用法id']}: {usage.get('词性', '')} - {usage.get('含义', '')[:30]}...")

def verify_usage_ids(json_file):
    """验证用法ID是否正确添加"""
    
    with open(json_file, 'r', encoding='utf-8') as f:
        words_data = json.load(f)
    
    print(f"=== 验证用法ID ===")
    
    total_usages = 0
    correct_ids = 0
    
    for word_data in words_data:
        word_name = word_data.get('实词', '')
        usages = word_data.get('用法', [])
        
        for i, usage in enumerate(usages):
            total_usages += 1
            expected_id = i + 1
            actual_id = usage.get('用法id')
            
            if actual_id == expected_id:
                correct_ids += 1
            else:
                print(f"错误: {word_name} 用法{i+1} 的ID应该是{expected_id}，实际是{actual_id}")
    
    print(f"总用法数: {total_usages}")
    print(f"正确ID数: {correct_ids}")
    print(f"准确率: {correct_ids/total_usages*100:.1f}%")

if __name__ == "__main__":
    # 添加用法ID
    add_usage_ids("wenyan_words_enhanced.json", "wenyan_words_with_usage_ids.json")
    
    # 验证结果
    verify_usage_ids("wenyan_words_with_usage_ids.json")
