#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pdfplumber
import re

def extract_pdf_content(pdf_path):
    """提取PDF内容"""
    all_text = ""
    
    with pdfplumber.open(pdf_path) as pdf:
        print(f"PDF总页数: {len(pdf.pages)}")
        
        for page_num, page in enumerate(pdf.pages, 1):
            print(f"正在处理第 {page_num} 页...")
            text = page.extract_text()
            if text:
                all_text += text + "\n"
    
    return all_text

def clean_text(text):
    """清理文本"""
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        # 去掉大标题
        if "小笼妈2023年初中实词虚词表" in line:
            continue
            
        # 去掉页眉
        if "睿爸小屋公众号" in line:
            continue
            
        # 去掉页脚
        if re.match(r'^第\s*\d+\s*页\s*$', line):
            continue
            
        # 去掉训练第x天
        if re.match(r'.*训练第\d+天.*', line):
            continue
            
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def parse_entries_simple(text):
    """简单解析词条"""
    entries = []
    lines = text.split('\n')
    
    current_entry = None
    entry_id = 1
    
    for line in lines:
        line = line.strip()
        
        if not line:
            continue
            
        # 跳过表头
        if line.startswith('序号') and '例词' in line:
            continue
            
        # 检查是否是完整的词条行（包含序号、例词、词义、例句、出处）
        # 格式：序号 例词 词义 例句 出处
        parts = line.split()
        
        # 如果行以数字开头，可能是新词条
        if parts and parts[0].isdigit():
            # 保存之前的词条
            if current_entry:
                entries.append(current_entry)
            
            # 创建新词条
            current_entry = {
                'id': parts[0],
                'word': '',
                'meaning': '',
                'examples': []
            }
            
            # 尝试解析词条信息
            if len(parts) >= 2:
                current_entry['word'] = parts[1]
            
            # 查找词义和例句
            remaining_text = ' '.join(parts[2:]) if len(parts) > 2 else ''
            
            # 使用正则表达式分离词义、例句和出处
            pattern = r'(.*?)《([^》]+)》（([^）]+)）'
            match = re.search(pattern, remaining_text)
            
            if match:
                meaning_and_example = match.group(1).strip()
                source = match.group(2)
                grade = match.group(3)
                
                # 分离词义和例句
                # 词义通常以①②③等开头
                meaning_match = re.match(r'([①②③④⑤⑥⑦⑧⑨⑩][^①②③④⑤⑥⑦⑧⑨⑩]*)', meaning_and_example)
                if meaning_match:
                    meaning_part = meaning_match.group(1)
                    example_part = meaning_and_example[len(meaning_part):].strip()
                    
                    current_entry['meaning'] = meaning_part
                    
                    if example_part:
                        current_entry['examples'].append({
                            'content': example_part,
                            'source': source,
                            'grade': grade
                        })
                else:
                    # 如果没有找到词义标记，整个作为词义
                    current_entry['meaning'] = meaning_and_example
                    current_entry['examples'].append({
                        'content': '',
                        'source': source,
                        'grade': grade
                    })
            else:
                # 没有找到出处信息，整个作为词义
                current_entry['meaning'] = remaining_text
        
        elif current_entry:
            # 继续处理当前词条的其他行
            # 检查是否包含出处信息
            pattern = r'(.*?)《([^》]+)》（([^）]+)）'
            match = re.search(pattern, line)
            
            if match:
                example_content = match.group(1).strip()
                source = match.group(2)
                grade = match.group(3)
                
                current_entry['examples'].append({
                    'content': example_content,
                    'source': source,
                    'grade': grade
                })
            else:
                # 检查是否是词义行（以①②③等开头）
                if re.match(r'^[①②③④⑤⑥⑦⑧⑨⑩]', line):
                    if current_entry['meaning']:
                        current_entry['meaning'] += ' ' + line
                    else:
                        current_entry['meaning'] = line
                else:
                    # 可能是例句内容的延续
                    if current_entry['examples'] and not current_entry['examples'][-1]['content']:
                        current_entry['examples'][-1]['content'] = line
    
    # 添加最后一个词条
    if current_entry:
        entries.append(current_entry)
    
    return entries

def format_to_markdown(entries):
    """格式化为markdown"""
    markdown_content = "# 中考文言虚实词\n\n"
    
    for entry in entries:
        markdown_content += f"## 例词id：{entry['id']}\n"
        markdown_content += f"例词：{entry['word']}\n"
        
        if entry['meaning']:
            markdown_content += f"词义：{entry['meaning']}\n"
        
        for i, example in enumerate(entry['examples'], 1):
            if example['content'] or example['source'] or example['grade']:
                markdown_content += f"例句{i}：\n"
                markdown_content += f"-内容：{example['content']}\n"
                markdown_content += f"-出处：《{example['source']}》\n"
                markdown_content += f"-年级：（{example['grade']}）\n"
        
        markdown_content += "\n"
    
    return markdown_content

def main():
    pdf_path = "中考文言虚实词.pdf"
    
    print("开始提取PDF内容...")
    raw_text = extract_pdf_content(pdf_path)
    
    print("清理文本...")
    cleaned_text = clean_text(raw_text)
    
    # 保存清理后的文本用于调试
    with open("cleaned_simple.txt", "w", encoding="utf-8") as f:
        f.write(cleaned_text)
    
    print("解析词条...")
    entries = parse_entries_simple(cleaned_text)
    
    print(f"共解析出 {len(entries)} 个词条")
    
    print("生成markdown...")
    markdown_content = format_to_markdown(entries)
    
    # 保存markdown文件
    with open("中考文言虚实词_简单版.md", "w", encoding="utf-8") as f:
        f.write(markdown_content)
    
    print("转换完成！文件已保存为 中考文言虚实词_简单版.md")
    
    # 显示前几个词条作为预览
    print("\n前5个词条预览：")
    for i, entry in enumerate(entries[:5]):
        print(f"词条{i+1}: ID={entry['id']}, 词={entry['word']}, 词义={entry['meaning'][:50]}...")

if __name__ == "__main__":
    main()
