#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pdfplumber
import re

def extract_pdf_content(pdf_path):
    """提取PDF内容并解析为结构化数据"""
    
    all_text = ""
    
    # 打开PDF文件
    with pdfplumber.open(pdf_path) as pdf:
        print(f"PDF总页数: {len(pdf.pages)}")
        
        for page_num, page in enumerate(pdf.pages, 1):
            print(f"正在处理第 {page_num} 页...")
            
            # 提取页面文本
            text = page.extract_text()
            if text:
                all_text += text + "\n"
    
    return all_text

def clean_text(text):
    """清理文本，去掉页眉页脚等"""
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        
        # 跳过空行
        if not line:
            continue
            
        # 去掉大标题（包含"小笼妈2023年初中实词虚词表"）
        if "小笼妈2023年初中实词虚词表" in line:
            continue
            
        # 去掉页眉（睿爸小屋公众号）
        if "睿爸小屋公众号" in line:
            continue
            
        # 去掉页脚（第X页）
        if re.match(r'^第\s*\d+\s*页\s*$', line):
            continue
            
        # 去掉训练第x天
        if re.match(r'.*训练第\d+天.*', line):
            continue
            
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def parse_word_entries(text):
    """解析词条数据"""
    entries = []
    lines = text.split('\n')

    current_entry = None

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        if not line:
            i += 1
            continue

        # 跳过表头
        if line.startswith('序号') and '例词' in line:
            i += 1
            continue

        # 检查是否是新词条开始（以数字开头，后跟空格和汉字）
        match = re.match(r'^(\d+)\s+(\S+)\s+(.+)', line)
        if match:
            # 保存之前的词条
            if current_entry:
                entries.append(current_entry)

            entry_id = match.group(1)
            word = match.group(2)
            rest_content = match.group(3)

            current_entry = {
                'id': entry_id,
                'word': word,
                'meanings': [],
                'examples': []
            }

            # 解析词义和例句
            parse_meaning_and_examples(rest_content, current_entry)

        elif current_entry:
            # 继续解析当前词条的内容
            parse_meaning_and_examples(line, current_entry)

        i += 1

    # 添加最后一个词条
    if current_entry:
        entries.append(current_entry)

    return entries

def parse_meaning_and_examples(content, entry):
    """解析词义和例句内容"""
    # 检查是否包含词义标记（①②③等）
    if re.match(r'^[①②③④⑤⑥⑦⑧⑨⑩]', content):
        # 这是一个新的词义行
        # 分离词义和例句
        parts = re.split(r'《([^》]+)》（([^）]+)）', content)
        if len(parts) >= 4:
            # 有完整的例句信息
            meaning_and_example = parts[0].strip()
            source = parts[1]
            grade = parts[2]

            # 进一步分离词义和例句内容
            # 寻找词义结束的位置（通常在第一个句号或者明显的例句开始处）
            meaning_end = find_meaning_end(meaning_and_example)
            if meaning_end > 0:
                meaning = meaning_and_example[:meaning_end].strip()
                example_content = meaning_and_example[meaning_end:].strip()
            else:
                meaning = meaning_and_example
                example_content = ""

            entry['meanings'].append(meaning)

            if example_content:
                entry['examples'].append({
                    'content': example_content,
                    'source': source,
                    'grade': grade
                })
        else:
            # 只有词义，没有完整的例句信息
            entry['meanings'].append(content)

    elif '《' in content and '》' in content:
        # 这可能是单独的例句行
        parts = re.split(r'《([^》]+)》（([^）]+)）', content)
        if len(parts) >= 4:
            example_content = parts[0].strip()
            source = parts[1]
            grade = parts[2]

            entry['examples'].append({
                'content': example_content,
                'source': source,
                'grade': grade
            })
    else:
        # 其他内容，可能是词义的延续或者例句内容
        if entry['meanings'] and not entry['examples']:
            # 可能是词义的延续
            entry['meanings'][-1] += ' ' + content
        elif entry['examples']:
            # 可能是例句内容的延续
            if not entry['examples'][-1]['content']:
                entry['examples'][-1]['content'] = content
            else:
                entry['examples'][-1]['content'] += ' ' + content

def find_meaning_end(text):
    """找到词义结束的位置"""
    # 寻找可能的词义结束标志
    for i, char in enumerate(text):
        if char in '。！？' and i < len(text) - 1:
            # 检查后面是否有明显的例句开始标志
            remaining = text[i+1:].strip()
            if remaining and (remaining[0].isupper() or remaining[0] in '一二三四五六七八九十'):
                return i + 1

    # 如果没找到明确的分界点，尝试其他方法
    # 寻找明显的例句开始（通常是人名、地名或者引用）
    common_patterns = [
        r'[A-Z][a-z]+',  # 英文人名
        r'[一二三四五六七八九十]+[年月日]',  # 时间
        r'[《][^》]+[》]',  # 书名
    ]

    for pattern in common_patterns:
        match = re.search(pattern, text)
        if match:
            return match.start()

    return 0

def format_to_markdown(entries):
    """将词条格式化为markdown"""
    markdown_content = "# 中考文言虚实词\n\n"
    
    for entry in entries:
        markdown_content += f"## 例词id：{entry['id']}\n"
        markdown_content += f"例词：{entry['word']}\n"
        
        if entry['meanings']:
            markdown_content += f"词义：{entry['meanings'][0]}\n"
        
        for i, example in enumerate(entry['examples'], 1):
            markdown_content += f"例句{i}：\n"
            if 'content' in example:
                markdown_content += f"-内容：{example['content']}\n"
            if 'source' in example:
                markdown_content += f"-出处：{example['source']}\n"
            if 'grade' in example:
                markdown_content += f"-年级：{example['grade']}\n"
        
        markdown_content += "\n"
    
    return markdown_content

def main():
    pdf_path = "中考文言虚实词.pdf"
    
    print("开始提取PDF内容...")
    raw_text = extract_pdf_content(pdf_path)
    
    print("清理文本...")
    cleaned_text = clean_text(raw_text)
    
    # 保存清理后的文本用于调试
    with open("cleaned_text.txt", "w", encoding="utf-8") as f:
        f.write(cleaned_text)
    
    print("解析词条...")
    entries = parse_word_entries(cleaned_text)
    
    print(f"共解析出 {len(entries)} 个词条")
    
    print("生成markdown...")
    markdown_content = format_to_markdown(entries)
    
    # 保存markdown文件
    with open("中考文言虚实词.md", "w", encoding="utf-8") as f:
        f.write(markdown_content)
    
    print("转换完成！文件已保存为 中考文言虚实词.md")
    
    # 显示前几个词条作为预览
    print("\n前3个词条预览：")
    for i, entry in enumerate(entries[:3]):
        print(f"词条{i+1}: {entry}")

if __name__ == "__main__":
    main()
